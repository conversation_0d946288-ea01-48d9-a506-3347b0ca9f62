<script setup lang="ts">
import CarStepLayout from '@/PageComponents/CarBeforeApplyLoanStep/components/CarStepLayout.vue'
import PlateNumber from '../form/components/PlateNumber/index.vue'
import RegionPicker from '../form/components/RegionPicker/index.vue'
import CarStatus from '../form/components/CarStatus/index.vue'
// import IdentityCard from '../form/components/IdentityCard/index.vue'
// import IconCheckWhite from '@/components/IconCheckWhite/index.vue'
import MyNavBar from '@/PageComponents/CarBeforeApplyLoanStep/components/MyNavBar.vue'
import stepBg from '@/assets/images/p2025070201/step_bg2.png'

import { isIOS } from '@/utils/device'
import { useForm } from '@/pages/composables/useForm'
import { TrackClickButton, useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import { usePageBackInterceptor } from '@/composables/usePageBackInterceptor'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { isLogin } from '@/utils/auth'
import { useDialog } from '@revfanc/use'
import dialogMount from '@/utils/dialog-mount'
import { useCarBeforeApplyLoanStep } from '@/composables/useCarBeforeApplyLoanStep'

defineOptions({
  beforeRouteEnter(to, from, next) {
    if (isLogin()) {
      next()
    }
    else {
      next({
        path: from.path || to.path.replace('/form', ''),
        query: {
          ...from.query,
        },
      })
    }
  },
})

const DialogAmountIntercept = dialogMount(() => import('@/pages/landing-page/components/Dialog/PageInterceptIdentityV2/index.vue'))
// const DialogLoginUpdate = defineAsyncComponent(() => import('@/pages/landing-page/p2025040901/components/DialogLoginUpdate/index.vue'))
// const DialogForm = defineAsyncComponent(() => import('../form/components/DialogForm/index.vue'))

const isBack = ref(false)

const keyboardFocus = ref(false)

const pageBackInterceptor = usePageBackInterceptor()
const unionLogin = useUnionLogin()

const dialog = useDialog()

const { goBack } = useCarBeforeApplyLoanStep({ autoTrack: false })

const {
  info,
  region,
  realNameRef,
  realName,
  plateNumberRef,
  plateNumber,
  carStatus,
  // readProtocol,
  // agreementCheckTime,
  // isUnionLogin,
  // showProtocol,
  getInfo,
  // goPicture,
  applyLoan,
} = useForm({
  isClue: true,
  disableCheckRealName: false,
  disableProtocol: true,
  disableCheckRegion: true,
  isCheckIdentityCardByDialog: true,
  isInstitutionalAgreementVisible: true,
  footer: {
    record: 'CQ',
    footerTips: 'no',
  },
})

// const showIdentityCard = computed(() => plateNumber.value?.length >= 7)
// const showKeyboard = computed(() => plateNumberRef?.value?.showKeyboard)
const realNameFocus = ref<boolean>(false)

const TRACK_PAGE = 'userInfoPage'

const track = useTrack()

watch(() => keyboardFocus.value, (val) => {
  if (val) {
    scrollPageToMiddle('plateNumber')
  }
})

// watch(() => plateNumber.value, (newValue) => {
//   if (newValue?.length >= 7 && keyboardFocus.value) {
//     scrollPageToMiddle('identityCard')
//   }
// })

watch(() => realNameFocus.value, () => {
  scrollPageToMiddle('realName')
})

function trackClick(button) {
  track.click({
    page: TRACK_PAGE,
    button,
  })
}

function handleBack() {
  trackClick(TrackClickButton.BACK)

  if (!info.value?.licensePlateNumber && plateNumber.value?.length < 7 && !isBack.value) {
    showDialogLeave()
    isBack.value = true
    return false
  }
  // if ((!info.value?.idCardFrontUrl || !info.value?.idCardBackUrl) && (!identityCards.value?.front || !identityCards.value?.back) && !isBack.value) {
  //   showDialogForm('identityCard')
  //   isBack.value = true
  //   return false
  // }

  return true
}

function showDialogLeave() {
  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogAmountIntercept, {
        onAction: context.callback,
      })
    },
  }).then((res) => {
    if (res.action === 'confirm') {
      getInfo()
    }
  })
}

// function showDialogForm(itemType) {
//   return dialog.open({
//     render(context) {
//       return h(DialogForm, {
//         itemType,
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'confirm') {
//       getInfo()
//     }
//   })
// }

// function uploadTwice() {
//   if (identityCards.value.front && identityCards.value.back) {
//     trackClick(TrackClickButton.FINISH_ID)
//   }
// }

async function scrollPageToMiddle(type: string) {
  const formEl: HTMLElement | null = document.querySelector('#form')
  if (!formEl) {
    return
  }
  const formTop = formEl.offsetTop

  let scrollTop: number

  if (type === 'plateNumber') {
    scrollTop = formTop + (isIOS ? 160 : 140)
  }
  else {
    scrollTop = formTop
  }

  setTimeout(() => {
    window.scrollTo({
      top: scrollTop,
      behavior: 'smooth',
    })
  }, 100)
}

// function navBackIconClick() {
//   const shouldBack = handleBack()

//   if (shouldBack) {
//     window.history.back()
//   }
// }

// function showDialogLoginUpdate() {
//   return dialog.open({
//     render(context) {
//       return h(DialogLoginUpdate, {
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'success') {
//       window.location.reload()
//     }
//     return Promise.reject(res)
//   })
// }

// function toggleProtocol() {
//   readProtocol.value = !readProtocol.value
//   if (readProtocol.value) {
//     agreementCheckTime.value = Date.now()
//   }
// }

function onNext() {
  realNameRef.value?.blur()

  setTimeout(() => {
    plateNumberRef.value?.handleShowKeyboard()
  }, 400)
}

onMounted(() => {
  setTimeout(() => {
    if (realNameRef.value && !realName.value) {
      realNameRef.value.focus()
      realNameFocus.value = true
    }
    else {
      plateNumberRef.value?.handleShowKeyboard()
    }
  }, 1000)

  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
    })
  })

  track.stay({
    page: TRACK_PAGE,
  })

  // 属于联登录需要设置返回到指定页面
  if (unionLogin.isUnionLogin.value) {
    pageBackInterceptor.setBackHandler()
  }

  pageBackInterceptor.add(handleBack)
})

onBeforeUnmount(() => {
  pageBackInterceptor.remove()
})
</script>

<template>
  <CarStepLayout class="car-staging-stap-layout" record="CQ" :step-bg="stepBg" record-margin-top="100px">
    <template #header>
      <MyNavBar title="额度评估中" @back="goBack()" />
    </template>

    <div class="apply-loan-container">
      <!-- <img src="@/assets/images/p2025070201/title-3.png" alt="form-title" class="form-title"> -->

      <form id="form" class="form" @submit.prevent>
        <!-- Name Section -->
        <div class="form-section">
          <div class="section-title mb-0">
            <span>真实姓名</span>
          </div>
          <div class="row input-row">
            <input
              ref="realNameRef"
              v-model.trim="realName"
              class="label input "
              type="text"
              placeholder="请输入您的姓名"
              maxlength="11"
              enterkeyhint="next"
              @input="($event: any) => realName = $event.target.value.replace(/[^a-zA-Z\u4e00-\u9fa5]/g, '')"
              @focus="() => { trackClick(TrackClickButton.NAME);realNameFocus = true; }"
              @blur="() => { realNameFocus = false;realName && trackClick(TrackClickButton.FINISH_NAME) }"
              @keyup.enter="onNext"
            >
          </div>
        </div>

        <!-- Plate Number Section -->
        <div class="form-section">
          <div class="section-title">
            <span>车牌号</span>
            <span class="right-text">获取额度唯一凭证，请仔细填写</span>
          </div>
          <!-- 车牌号 -->
          <PlateNumber
            id="plate-number" ref="plateNumberRef" v-model="plateNumber" class="plate"
            @invoke="trackClick(TrackClickButton.CAR)" @done="trackClick(TrackClickButton.FINISH)"
            @focus="keyboardFocus = true" @blur="keyboardFocus = false"
          />
        </div>

        <div class="form-section">
          <div class="section-title">
            <span>车辆状态</span>
          </div>
          <!-- 车辆状态 -->
          <CarStatus v-model="carStatus" @change="trackClick(TrackClickButton.CAR_STATUS)" />
        </div>

        <!-- Location Section -->
        <div v-show="false" class="form-section">
          <RegionPicker v-model="region" @invoke="trackClick(TrackClickButton.CITY)">
            <template #default="{ handleSelect }">
              <div class="row location" @click="handleSelect">
                <div class="label">
                  您当前所在地
                </div>
                <div class="value-row">
                  <div class="value">
                    {{ region.cityName || '请选择' }}
                  </div>
                  <div class="arrow" />
                </div>
              </div>
            </template>
          </RegionPicker>
        </div>

        <!-- 上传身份证 -->
        <!-- <IdentityCard
          v-if="showIdentityCard"
          class="identity-card"
          :info="info"
          :identity-cards="identityCards"
          @picture="goPicture($event).finally(() => { uploadTwice() }), trackClick(TrackClickButton.ID)"
        /> -->

        <!-- <div class="footer" :class="{ 'footer-fixed': showKeyboard }">
          <button class="submit-btn bounce" @click="applyLoan(), trackClick(TrackClickButton.GET_QUOTA)" /> -->

        <!-- 协议 -->
        <!-- <div class="protocol" @click="toggleProtocol">
            <IconCheckWhite class="icon" :checked="readProtocol" />
            <span class="text">
              我已阅读并同意
              <span class="blue" @click.stop="showProtocol(isUnionLogin ? 'privacy-policy' : 'user-share')">《授权相关协议》</span>
            </span>
          </div>
        </div> -->

        <button class="submit-btn bounce" @click="applyLoan(), trackClick(TrackClickButton.GET_QUOTA)" />
        <!-- <div class="protocol" @click="toggleProtocol">
          <IconCheckWhite class="icon" :checked="readProtocol" />
          <span class="text">
            我已阅读并同意
            <span class="blue" @click.stop="showProtocol(isUnionLogin ? 'privacy-policy' : 'user-share')">《授权相关协议》</span>
          </span>
        </div> -->
      </form>
    </div>

    <!-- <div class="footer-placeholder" :class="{ 'footer-m-top': showKeyboard }" /> -->
  </CarStepLayout>
</template>

<style scoped lang="less">
// .car-staging-stap-layout {
//   padding-bottom: 80px;
// }

.apply-loan-container {
  position: relative;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  padding: 30px 10px 13px;
  margin-top: 20px;

  .form-title {
    position: absolute;
    top: 0;
    left: 50%;
    height: 34px;
    transform: translateX(-50%);
  }

  .form {
    display: flex;
    flex-direction: column;

    .form-section {
      &:not(:first-child) {
        margin-top: 30px;
      }
    }

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      &.mb-0 {
        margin-bottom: 0;
      }
      &::before {
        content: '';
        width: 2px;
        height: 12px;
        background-color: #1677ff;
        margin-right: 4px;
      }

      .right-text {
        font-weight: 400;
        font-size: 12px;
        color: #999;
        margin-top: 2px;
        margin-left: 8px;
      }

      span {
        font-weight: 500;
        font-size: 16px;
        color: #333;
      }
    }

    .row {
      padding: 15px;
      background: #f4f4f4;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 8px;

      .label {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }

      .input {
        color: #333;
        background: transparent;
        width: 100%;
        padding: 0 0;
      }

      .value-row {
        display: flex;
        align-items: center;

        .value {
          font-weight: 500;
          font-size: 14px;
          color: #333;
        }

        .arrow {
          width: 5px;
          height: 10px;
          margin-left: 8px;
          background-image: url('@/pages/landing-page/p2025040901/images/icon_back.png');
          background-size: 100% 100%;
          transform: rotate(270deg);
        }
      }
    }

    .input-row {
      background-color: transparent;
      padding: 15px 10px;
      border-bottom: 1px solid #eee;
    }

    .car-status {
      padding: 0 6px;
      border: none !important;
      .row {
        margin-bottom: 0 !important;
      }
    }

    .location {
      padding-left: 0;
      padding-right: 7px;
      background: #fff;
      margin-top: -10px;
    }

    :deep(.plate-input-wrapper .plate-input .item) {
      margin: 0 2.5px;
    }
  }

  .footer {
    width: 100%;
    background: #ffffff;
    box-shadow: 0px -1px 5px 0px rgba(47, 121, 255, 0.1);
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 19;
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);

    &.footer-fixed {
      bottom: 268px;
    }
  }

  .submit-btn {
    display: block;
    width: 331px;
    height: 61px;
    margin: 43px auto 15px;
    position: relative;
    background-image: url('@/pages/landing-page/p2025040901/images/btn.png');
    background-size: 100% 100%;
  }
  .protocol {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    text-align: center;
    margin: -5px auto 10px;

    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-right: 5px;

      &.active {
        color: #1677ff;
      }
    }

    .text {
      vertical-align: middle;

      .blue {
        color: #1677ff;
      }
    }
  }
}

.footer-placeholder {
  height: calc(40px + env(safe-area-inset-bottom));
  height: calc(40px + constant(safe-area-inset-bottom));

  &.footer-m-top {
    margin-top: 50px;
  }
}
</style>
