<script setup lang="ts">
import { useOpenInstall } from '@/composables/useOpenInstall'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'

const emit = defineEmits(['action'])

const track = useTrack()

const { download, install } = useOpenInstall()

// 埋点按钮值定义
const TRACK_BUTTONS = {
  CLOSE: 1, // 关闭按钮
  DOWNLOAD: 2, // 下载领取
} as const

onMounted(() => {
  install()
  // 弹窗显示时发送显示埋点
  track.show({ page: 'androidBackInterceptorPopup' })
})

function handleClose() {
  // 点击关闭按钮埋点
  track.click({ page: 'androidBackInterceptorPopup', button: TRACK_BUTTONS.CLOSE })

  emit('action', { action: 'close' })
}

function handleDownload() {
  // 点击下载领取按钮埋点
  track.click({ page: 'androidBackInterceptorPopup', button: TRACK_BUTTONS.DOWNLOAD })
  download()

  emit('action', { action: 'close' })
}
</script>

<template>
  <div class="dialog">
    <img class="close" src="@/pages/landing-page/p2025032401/images/close.png" @click="handleClose">
    <div class="content" @click="handleDownload">
      <div class="btn-download bounce">
        下载领取
        <img class="tips" src="@/pages/landing-page/p2025032401/images/tips_2.png">
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.dialog {
  width: 375px;
  height: 436px;
  background: url('@/pages/landing-page/p2025032401/images/banner_3.png') no-repeat top center;
  background-size: 100% auto;
  text-align: center;
  display: flex;
  flex-direction: column;

  .close {
    position: absolute;
    top: 82px;
    right: 12px;
    width: 21px;
    height: 21px;
    cursor: pointer;
  }

  .content {
    margin-top: auto;
    margin-bottom: 44px;
    padding: 0 20px;

    .btn-download {
      position: relative;
      width: 300px;
      height: 49px;
      background: #1677ff;
      border-radius: 9999px;
      color: #ffffff;
      font-size: 18px;
      font-weight: 500;
      line-height: 49px;
      margin: 0 auto;
      cursor: pointer;
      box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);

      .tips {
        position: absolute;
        top: -20px;
        right: -10px;
        width: 80px;
        height: 24px;
      }
    }
  }
}
</style>
