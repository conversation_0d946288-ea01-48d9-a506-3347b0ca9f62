<script setup lang="ts">
import iconFailure from '@/pages/landing-page/p2025032401/images/status_failure.png'

import dayjs from 'dayjs'

defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<template>
  <div class="status flex flex-col">
    <img class="icon" :src="iconFailure">
    <div class="title">
      额度获取失败
    </div>
    <div class="subtitle">
      {{ info?.licensePlateNumber }} 申请时间：{{ dayjs().format('YYYY/MM/DD') }}
    </div>
  </div>
</template>

<style lang='less' scoped>
.status {
  .icon {
    width: 52px;
    height: 52px;
    margin: 30px auto 0;
  }

  .title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
    margin: 15px auto 0;
    text-align: center;
  }

  .subtitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 17px;
    margin: 5px auto 0;
    text-align: center;
  }
}
</style>
