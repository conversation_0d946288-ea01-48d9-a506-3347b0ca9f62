<script setup lang="ts">
const props = defineProps({
  info: {
    type: Object,
    default: () => { },
  },
  identityCards: {
    type: Object,
    default: () => {
      return {
        front: '',
        back: '',
      }
    },
  },
  styleType: {
    type: String,
    default: 'default',
  },
})

const emit = defineEmits(['picture'])

const pictures = computed(() => {
  return {
    front: props.identityCards.front || props.info.idCardFrontUrl,
    back: props.identityCards.back || props.info.idCardBackUrl,
  }
})

function goPicture(type) {
  emit('picture', type)
}
</script>

<template>
  <div :class="[`identity-card--${styleType}`]">
    <div class="card flex items-center justify-between">
      <div class="item" :class="{ mask: pictures.front }" @click="goPicture('front')">
        <img v-if="pictures.front || identityCards.front" class="img" :src="pictures.front">
        <div class="overlay" />
      </div>
      <div class="item" :class="{ mask: pictures.back }" @click="goPicture('back')">
        <img v-if="pictures.back" class="img" :src="pictures.back">
        <div class="overlay" />
      </div>
    </div>
  </div>
</template>

<style lang='less' scoped>
.identity-card--default {
  width: 351px;
  height: 195px;
  background-image: url('@/pages/landing-page/p2025040901/images/bg_idcard.png');
  background-size: 100% 100%;

  .card {
    margin: 82px 20px 0;

    .item {
      width: 150px;
      height: 90px;
      background-size: 100% 100%;
      position: relative;
      border-radius: 8px;
      overflow: hidden;

      &:nth-child(1) {
        background-image: url('@/assets/images/home/<USER>');
      }

      &:nth-child(2) {
        background-image: url('@/assets/images/home/<USER>');
      }

      .img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .overlay {
        display: none;
      }

      &.mask {
        background-image: none;

        .img {
          filter: blur(3px);
        }

        .overlay {
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.3);
          z-index: 9;
        }

        &::after {
          content: '点击切换';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 10;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
        }
      }

      &.disabled {
        &::after {
          display: none;
        }
      }
    }
  }
}

.identity-card--dialog {
  width: 295px;
  height: 112px;
  background: #ffffff;
  box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.05);
  border-radius: 8px;

  .card {
    padding: 10px;
    display: flex;
    justify-content: space-between;

    .item {
      width: 133px;
      height: 92px;
      background: #fbfdff;
      border-radius: 8px;
      border: 1px solid #e7f5ff;
      background-size: cover;
      background-position: center center;
      position: relative;
      border-radius: 8px;
      overflow: hidden;

      &:nth-child(1) {
        background-image: url('@/assets/images/home/<USER>');
      }

      &:nth-child(2) {
        background-image: url('@/assets/images/home/<USER>');
      }

      .img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .overlay {
        display: none;
      }

      &.mask {
        background-image: none;

        .img {
          filter: blur(3px);
        }

        .overlay {
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.3);
          z-index: 9;
        }

        &::after {
          content: '点击切换';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 10;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          line-height: 20px;
        }
      }

      &.disabled {
        &::after {
          display: none;
        }
      }
    }
  }
}
</style>
