<script setup lang="ts">
import PageFooter from '@/components/PageFooter/index.vue'
import PageFooter2 from '@/components/PageFooter/PageFooter2.vue'

withDefaults(defineProps<{
  stepBg: string
  record?: string
  contentPadding?: string
  recordMarginTop?: string
  footerTips?: boolean
}>(), {
  recordMarginTop: 'auto',
  contentPadding: '0 15px',
})
</script>

<template>
  <div class="car-step-layout">
    <div class="header">
      <slot name="header" />
    </div>

    <div class="step-bg">
      <img :src="stepBg" alt="step_bg">
    </div>

    <div class="content">
      <slot />
    </div>

    <PageFooter
      v-if="record && record === 'HZ'"
      class="custom-footer"
      company-name="杭州优易润科技有限公司"
      icp="浙ICP备2025148745号-3"
      phone="4008602166"
    />
    <PageFooter
      v-else-if="record && record === 'CQ'"
      class="custom-footer"
    />
    <PageFooter2 v-if="footerTips" />
  </div>
</template>

<style scoped lang="less">
.car-step-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #ecf5fc;
  overflow: auto;

  .step-bg {
    width: 100%;
    height: 194px;
  }

  .content {
    padding: v-bind(contentPadding);
  }

  :deep(.custom-footer) {
    margin-top: v-bind(recordMarginTop) !important;
    margin-bottom: 40px !important;
  }
}
</style>
