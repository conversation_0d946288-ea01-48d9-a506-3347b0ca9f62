<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'

interface Props {
  progress?: number // 进度值 0-1
  duration?: number
  width?: number
  height?: number
  tickCount?: number
  circleR?: number
  tickInnerDistance?: number // 刻度线离进度条圆弧内部的距离
  tickOuterDistance?: number // 刻度线离进度条圆弧外部的距离
  trackColor?: string // 轨道背景色
  lineColor?: string // 刻度线颜色
  lineWidth?: number// 刻度线的宽度
  endpointColor?: string// 端点的填充色
  endpointR?: number // 端点的半径
  endpointBorderWidth?: number// 端点边框宽度
  endpointBorderColor?: string // 端点边框颜色
}
const props = withDefaults(defineProps<Props>(), {
  progress: 0.7,
  duration: 2000,
  width: 300,
  height: 162,
  tickCount: 25,
  circleR: 126,
  tickInnerDistance: 20,
  tickOuterDistance: 14,
  trackColor: '#F3F3F5',
  lineColor: '#CFD0CD',
  lineWidth: 2,
  endpointColor: '#fff',
  endpointR: 6,
  endpointBorderWidth: 2,
  endpointBorderColor: '#1677FF',
})

const svgRef = ref<SVGElement | null>(null)

const targetPercent = computed(() => props.progress)
const { width, height, circleR: r } = props
const strokeWidth = 12
const cx = width / 2
const cy = height - 10
// 半圆弧路径
const arcPath = `
    M ${cx - r} ${cy}
    A ${r} ${r} 0 0 1 ${cx + r} ${cy}
  `

// 渐变
const progressGradient = 'url(#progressGradient)'

// SVG 渐变定义
onMounted(() => {
  const svg = svgRef.value
  if (svg && !svg.querySelector('#progressGradient')) {
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs')
    defs.innerHTML = `
        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#1677FF"/>
          <stop offset="100%" stop-color="#1677FF"/>
        </linearGradient>
      `
    svg.prepend(defs)
  }
})

// 刻度尺
const { tickCount, tickInnerDistance, tickOuterDistance } = props
const ticks = Array.from({ length: tickCount + 1 }, (_, i) => {
  const angle = Math.PI * (i / tickCount)
  const innerR = r - tickInnerDistance
  const outerR = r - tickOuterDistance
  return {
    x1: cx + innerR * Math.cos(Math.PI - angle),
    y1: cy - innerR * Math.sin(angle),
    x2: cx + outerR * Math.cos(Math.PI - angle),
    y2: cy - outerR * Math.sin(angle),
  }
})

// 动画进度
const progress = ref(0)
const arcLength = Math.PI * r
const progressOffset = computed(() => arcLength * (1 - progress.value))

// 端点指针坐标
const pointer = computed(() => {
  const angle = Math.PI * progress.value
  return {
    x: cx + r * Math.cos(Math.PI - angle),
    y: cy - r * Math.sin(angle),
  }
})
// 动画
onMounted(() => {
  let start = null
  function animate(ts) {
    if (!start)
      start = ts
    const elapsed = ts - start
    const percent = Math.min(elapsed / props.duration, 1)
    progress.value = percent * targetPercent.value
    if (percent < 1)
      requestAnimationFrame(animate)
  }
  requestAnimationFrame(animate)
})
</script>

<template>
  <svg ref="svgRef" :width="width" :height="height">
    <!-- 背景弧 -->
    <path
      :d="arcPath"
      :stroke="props.trackColor"
      fill="none"
      :stroke-width="strokeWidth"
      stroke-linecap="round"
    />
    <!-- 进度弧 -->
    <path
      :d="arcPath"
      :stroke="progressGradient"
      fill="none"
      :stroke-width="strokeWidth"
      stroke-linecap="round"
      :stroke-dasharray="arcLength"
      :stroke-dashoffset="progressOffset"
    />
    <!-- 刻度尺 -->
    <g>
      <line
        v-for="(tick, index) in ticks"
        :key="index"
        :x1="tick.x1"
        :y1="tick.y1"
        :x2="tick.x2"
        :y2="tick.y2"
        :stroke="props.lineColor"
        stroke-linecap="round"
        :stroke-width="props.lineWidth"
      />
    </g>
    <!-- 端点指针 -->
    <circle
      :cx="pointer.x"
      :cy="pointer.y"
      :r="props.endpointR"
      :fill="props.endpointColor"
      :stroke="props.endpointBorderColor"
      :stroke-width="props.endpointBorderWidth"
    />
    <!-- 文字 -->
    <!-- <text
      v-if="props.showText"
      :x="width / 2"
      :y="height / 2 + 10"
      text-anchor="middle"
      font-size="48"
      fill="#1677FF"
      font-weight="bold"
    >100万</text>
    <text
      v-if="props.showText"
      :x="width / 2"
      :y="height / 2 - 30"
      text-anchor="middle"
      font-size="16"
      fill="#B0B0B0"
    >贷款额度最高</text> -->
  </svg>
</template>
