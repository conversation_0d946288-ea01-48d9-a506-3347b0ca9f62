<script setup lang="ts">
import NavBar from '@/components/NavBar/index.vue'
import pageBackInterceptor from '@/utils/page-back-interceptor'
import { useOpenInstall } from '@/composables/useOpenInstall'
import { showLoadingToast } from 'vant'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import { useCountDown } from '@vant/use'
import { isIOS } from '@/utils'

const { download, install } = useOpenInstall()
const { current, start } = useCountDown({
  time: 8000,
})
const countdown = computed(() => {
  let time = current.value.total

  // 毫秒转秒
  time = Math.floor(time / 1000)

  return time
})

// 1 返回 2 下载
enum TRACK_BUTTONS {
  BACK = 1,
  DOWNLOAD = 2,
}

const TRACK_PAGE = 'downloadPage'
const track = useTrack()
const isBack = ref(false)

onMounted(() => {
  setTimeout(() => {
    start()
  }, 1000)
  install()
  pageBackInterceptor.add(handleBack)

  track.refresh().finally(() => {
    track.show({ page: TRACK_PAGE })
  })
})

async function handleBack() {
  track.click({ page: TRACK_PAGE, button: TRACK_BUTTONS.BACK })

  if (isBack.value) {
    if (isIOS) {
      await new Promise(resolve => setTimeout(resolve, 0))
    }
    return true
  }

  handleDownload()
  isBack.value = true
  if (isIOS) {
    await new Promise(resolve => setTimeout(resolve, 0))
  }
  return false
}

async function navBackIconClick() {
  const shouldBack = await handleBack()

  if (shouldBack) {
    window.history.back()
  }
}

function handleDownload(toast: boolean = true) {
  if (toast) {
    showLoadingToast('下载中...')
  }

  download()
}
</script>

<template>
  <div class="download-page">
    <NavBar :background-custom-style="{ backgroundColor: '#fff' }">
      <template #left>
        <img
          class="icon-back" src="@/pages/landing-page/p2025032401/images/icon_back.png"
          @click="navBackIconClick()"
        >
      </template>
    </NavBar>
    <img class="bg" src="@/pages/landing-page/p2025040901/images/bg_page.png">
    <button class="button" @click="handleDownload(), track.click({ page: TRACK_PAGE, button: TRACK_BUTTONS.DOWNLOAD })">
      下载中，预计<span class="text-#FFB500">{{ countdown }}S</span>完成
      <img class="tips" src="@/pages/landing-page/p2025040901/images/tips_4.png">
    </button>
  </div>
</template>

<style lang='less' scoped>
.download-page {
  width: 375px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;

  .icon-back {
    width: 9px;
    height: 15px;
    margin-left: 14px;
  }

  .bg {
    width: 375px;
    height: 724px;
    display: block;
  }

  .button {
    width: 321px;
    height: 50px;
    background: #1677ff;
    box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);
    border-radius: 9999px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #ffffff;
    line-height: 25px;
    position: absolute;
    top: 423px;
    left: 50%;
    margin-left: -160.5px;
    animation: bounce 0.3s ease-in-out infinite alternate;

    .tips {
      width: 82px;
      height: 22px;
      position: absolute;
      top: -12px;
      right: -9px;
    }
  }
}
</style>
