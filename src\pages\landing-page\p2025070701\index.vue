<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { isIOS } from '@/utils/device'

import Footer from '@/components/PageFooter/index.vue'
import IconCheckWhite from '@/components/IconCheckWhite/index.vue'
import pageBackInterceptor from '@/utils/page-back-interceptor'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import { useSmsLogin } from '@/composables/useSmsLogin'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { useRoute } from 'vue-router'
import { useDialog } from '@revfanc/use'
import { useNoBehavior } from '@/composables/useNoBehavior'
import { systemConfig } from '@/api/home'
import { useForm } from '@/pages/composables/useForm'
import { useProtocol } from '@/composables/useProtocol'
import { isInWechat } from '@/utils'

import DialogDownload from '@/components/Dialog/DialogDownload.vue'
import DialogLogin2 from '@/components/Dialog/DialogLogin2.vue'
import DialogSms from '@/components/Dialog/DialogSms.vue'
import RegionPicker from '@/components/RegionPicker/index.vue'
import { useFormTypeNavigation } from '@/composables/useloginAndAfter'

const { navigateToForm } = useFormTypeNavigation()
const track = useTrack()
const route = useRoute()
const dialog = useDialog()

const noBehavior = useNoBehavior()

dialog.interceptors.before.use((opts) => {
  noBehavior.pause()
  return opts
})

dialog.interceptors.after.use((opts) => {
  noBehavior.resume()
  return opts
})

const { showProtocol } = useProtocol()

const {
  phoneNumber,
  phoneNumberValid,
  phoneInputRef,
  verificationCode,
  verificationBtnText,
  verificationBtnDisabled,
  readProtocol,
  handleSms,
  handleLogin,
  handleNoSmsLogin,
  handleConsentAuthorization,
  handleAgreementClick,
  agreementDialogConfirmTime,
  setInitialPhoneNumber,
  editPhoneNumber,
} = useSmsLogin({ isIndex: true })

const {
  region,
} = useForm()

// 联合登录
const unionLogin = useUnionLogin()
unionLogin.handleLogin().then((res) => {
  const { success, mobile } = res

  if (mobile) {
    phoneNumber.value = mobile
  }

  if (success) {
    navigateToForm()
  }
})

// 埋点按钮值定义 1.返回 2.立即领取额度 3.手机号输入点击 4.手机号输入完成 5.验证码获取 6.验证码输入完成 7.勾选协议 8.归属地
enum TRACK_BUTTONS {
  BACK = 1, // 返回按钮
  GET_QUOTA = 2, // 立即领取额度
  PHONE_INPUT_CLICK = 3, // 手机号输入点击
  PHONE_INPUT_FINISH = 4, // 手机号输入完成
  CODE_INPUT_CLICK = 5, // 验证码获取
  CODE_INPUT_FINISH = 6, // 验证码输入完成
  PROTOCOL = 7, // 勾选协议
  CITY = 8, // 归属地
}

const TRACK_PAGE = 'landingPage'

// 是否点击了返回按钮
const isBack = ref(false)

watch(() => phoneNumber.value, (val) => {
  if (val.length === 11) {
    trackClick(TRACK_BUTTONS.PHONE_INPUT_FINISH)
  }
})

// 从 URL 获取并解析手机号
function getPhoneFromUrl() {
  const { mobileNo } = route.query
  if (mobileNo && typeof mobileNo === 'string') {
    try {
      const decodedPhone = window.atob(mobileNo)
      if (/^\d{11}$/.test(decodedPhone)) {
        return decodedPhone
      }
    }
    catch (error) {
      console.error('Failed to decode phone number:', error)
    }
  }
  return null
}

const loginRegion = useSessionStorage('CARS_REGION', {
  cityId: '',
  cityName: '',
  provinceId: '',
  provinceName: '',
})
watch(() => region.value, (val) => {
  loginRegion.value = val
  console.log('loginRegion', loginRegion.value)
})

onMounted(() => {
  if (!isIOS) {
    pageBackInterceptor.add(handleBack)
  }

  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
    })
  })

  track.stay({
    page: TRACK_PAGE,
  })

  // 用户无操作时,自动弹出登录弹窗
  noBehavior.add({
    callback: showDialogLogin,
  })

  // 如果 URL 中有手机号，设置初始值
  const urlPhone = getPhoneFromUrl()
  if (urlPhone) {
    setInitialPhoneNumber(urlPhone)
  }

  systemConfig('LANDINGPAGE_AGREEMENT_CONFIRM').then((res: any) => {
    if (res.code === 200) {
      readProtocol.value = String(res?.data) === '0'
    }
  })
})

function trackClick(button: TRACK_BUTTONS) {
  try {
    return track.click({
      page: TRACK_PAGE,
      button,
    })
  }
  catch (error) {
    return error
  }
}

// 返回按钮点击事件
async function handleBack() {
  if (isBack.value || isInWechat()) {
    return true
  }

  isBack.value = true

  // 显示下载弹窗
  showDialogDownload()
}

async function navBackIconClick() {
  const shouldBack = await handleBack()

  if (shouldBack) {
    window.history.back()
  }
}

function showDialogDownload() {
  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogDownload, {
        onAction: context.callback,
      })
    },
  })
}

function showDialogLogin() {
  if (phoneNumber.value)
    return Promise.resolve()

  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogLogin2, {
        onAction: context.callback,
      })
    },
  }).then((res) => {
    if (res.action === 'success') {
      navigateToForm()
    }
    return Promise.reject(res)
  })
}

function showDialogSms() {
  return dialog.open({
    render(context) {
      return h(DialogSms, {
        parentForm: {
          verificationBtnText,
          verificationBtnDisabled,
        },
        onAction: context.callback,
      })
    },
    beforeClose: (close, res) => {
      if (res.action === 'sms') {
        handleSms()
      }
      else if (res.action === 'code') {
        verificationCode.value = res.code
      }
      else if (res.action === 'submit') {
        trackClick(TRACK_BUTTONS.GET_QUOTA)
        agreementDialogConfirmTime.value = Date.now()
        handleLogin().then((loginRes) => {
          if (loginRes) {
            close(res)
            navigateToForm()
          }
        })
      }
      else {
        close(res)
      }
    },
  }).finally(() => {
    verificationCode.value = ''
  })
}

async function onSubmit() {
  if (!phoneNumberValid.value) {
    showToast('请输入正确的手机号')
    phoneInputRef.value?.focus()
    return
  }

  // 如果没有勾选协议，显示协议弹窗
  if (!readProtocol.value) {
    const result = await handleConsentAuthorization()
    // 如果用户在协议弹窗点击确认
    if (result?.action === 'confirm') {
      // 判断是否是 URL 中的手机号且未修改
      const urlPhone = getPhoneFromUrl()
      if (urlPhone && !editPhoneNumber.value) {
        handleNoSmsLogin().then(res => res && navigateToForm())
      }
      else {
        // 否则显示验证码弹窗
        showDialogSms()
      }
    }
    return
  }

  // 如果已勾选协议，判断是否是 URL 中的手机号且未修改
  const urlPhone = getPhoneFromUrl()
  if (urlPhone && !editPhoneNumber.value) {
    agreementDialogConfirmTime.value = Date.now()
    handleNoSmsLogin().then(res => res && navigateToForm())
    return
  }

  // 如果手机号已修改或不是从 URL 获取的，走普通验证码登录流程
  if (phoneNumberValid.value && !verificationCode.value) {
    showDialogSms()
    return
  }

  handleLogin().then(res => res && navigateToForm())
}

function checkProtocol() {
  readProtocol.value = !readProtocol.value
  handleAgreementClick()
  trackClick(TRACK_BUTTONS.PROTOCOL)
}
</script>

<template>
  <div class="home-page">
    <NavBar class="nav-bar" :background-custom-style="{ backgroundColor: '#fff' }">
      <template #left>
        <img
          class="back" src="@/assets/images/home/<USER>"
          @click="trackClick(TRACK_BUTTONS.BACK), navBackIconClick()"
        >
      </template>
    </NavBar>

    <div class="header">
      <img class="logo-text" src="@/assets/images/common/logo-text.png">
      <span class="title">｜钱拿走 车照开</span>
    </div>

    <div class="desc">
      <span class="desc-text-1">
        最高可借(元)
      </span>
      <span class="desc-text-2">
        车辆估值9成
      </span>
      <span class="desc-text-3">
        年利率6%起，借1000，天利息<span class="desc-text-3-num">低至0.16元</span>
      </span>
      <div class="icon-box">
        <div class="icon-item">
          <img class="icon" src="@/assets/images/p2025070701/icon-1.png">
          <span>最长48期</span>
        </div>
        <div class="icon-item ml-10">
          <img class="icon" src="@/assets/images/p2025070701/icon-2.png">
          <span>不押车</span>
        </div>
        <div class="icon-item">
          <img class="icon" src="@/assets/images/p2025070701/icon-3.png">
          <span>快至1小时放款</span>
        </div>
      </div>
    </div>

    <form class="form" @submit.prevent>
      <div class="form-item">
        <div class="form-region">
          <span class="label">归属地</span>
          <RegionPicker v-model="region" class="region-component" @invoke="trackClick(TRACK_BUTTONS.CITY)">
            <template #default="{ handleSelect }">
              <div class="row" @click="handleSelect">
                <div class="label">
                  {{ region.cityName || '*' }}
                </div>
                <div class="arrow" />
              </div>
            </template>
          </RegionPicker>
        </div>
      </div>

      <div class="form-item">
        <div class="form-input">
          <span class="label">手机号</span>
          <input
            ref="phoneInputRef" v-model="phoneNumber" type="tel" placeholder="请输入手机号码" maxlength="11"
            @click="trackClick(TRACK_BUTTONS.PHONE_INPUT_CLICK)"
          >
          <button class="send-code-btn" type="button" @click="trackClick(TRACK_BUTTONS.CODE_INPUT_CLICK), onSubmit()">
            获取验证码
          </button>
        </div>
      </div>

      <button class="submit bounce" type="button" @click="trackClick(TRACK_BUTTONS.GET_QUOTA), onSubmit()" />

      <div class="protocol">
        <IconCheckWhite class="icon" :checked="readProtocol" @click="checkProtocol" />
        <span class="text" @click="checkProtocol">我已阅读并同意</span>
        <div class="link" @click="showProtocol('privacy-policy')">
          《贷款相关协议》
        </div>
      </div>
    </form>

    <Footer class="footer" />
  </div>
</template>

<style scoped lang="less">
.home-page {
  width: 375px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #d1e1ff 0%, #ffffff 40%);
  padding: 24px 15px 0;

  .nav-bar {
    .back {
      width: 9px;
      height: 15px;
      margin-left: 14px;
    }
  }

  .header {
    display: flex;
    align-items: center;

    .logo-text {
      height: 26px;
    }

    .title {
      font-weight: 600;
      font-size: 16px;
      color: #000000;
    }
  }

  .desc {
    background: #ffffff;
    box-shadow: 0px 0px 3px 0px rgba(0, 49, 118, 0.3);
    border-radius: 8px;
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .desc-text-1 {
      font-size: 14px;
      color: #333;
      margin-top: 20px;
    }

    .desc-text-2 {
      font-size: 36px;
      color: #333;
      font-weight: 600;
      margin-top: 15px;
    }

    .desc-text-3 {
      font-size: 12px;
      color: #999;
      margin-top: 12px;

      .desc-text-3-num {
        font-size: 14px;
        color: #fd4700;
      }
    }

    .icon-box {
      width: 100%;
      display: flex;
      // align-items: center;
      // justify-content: space-evenly;
      justify-content: center;
      gap: 50px;
      margin: 30px 0 30px 10px;

      .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .icon {
          width: 36px;
          height: 36px;
        }

        span {
          font-size: 14px;
          color: #333;
          margin-top: 10px;
        }
      }
    }
  }

  .form {
    display: flex;
    flex-direction: column;
    // margin: -1px auto 0;

    .form-item {
      margin: 15px auto 0;
      display: flex;
      align-items: center;
      width: 321px;
      height: 52px;
      border-bottom: 1px solid #eee;

      .label {
        font-size: 16px;
        color: #666;
        margin-right: 50px;
      }

      .form-input {
        width: auto;
        flex: 1;
        display: flex;
        align-items: center;
        background: #fff;

        input {
          width: 100px;
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 22px;

          &::placeholder {
            font-size: 16px;
            color: #cdcdcd;
          }
        }

        .send-code-btn {
          width: 76px;
          height: 22px;
          background: linear-gradient(180deg, #31afff 0%, #1677ff 100%);
          border-radius: 11px;
          margin-left: auto;
          border: none;
          color: #fff;
          font-size: 12px;
          line-height: 24px;
        }
      }

      .form-region {
        width: auto;
        flex: 1;
        display: flex;
        align-items: center;
        background: #fff;

        .region-component {
          flex: 1;

          .row {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .label {
            color: #333;
          }

          .arrow {
            width: 7px;
            height: 14px;
            color: #cbcbcb;
            margin-left: auto;
            background-image: url('@/assets/images/common/right_arrow.png');
            background-size: 100% 100%;
          }
        }
      }
    }

    .submit {
      width: 331px;
      height: 61px;
      display: block;
      margin: 35px auto 0;
      background-image: url('@/pages/landing-page/p2025040901/images/btn.png');
      background-size: 100% 100%;
    }

    .protocol {
      margin-top: 18px;
      height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon {
        width: 12px;
        height: 12px;
        margin-right: 5px;
      }

      .text {
        font-size: 12px;
        color: #666;
      }

      .link {
        font-size: 12px;
        color: #666;
        margin-left: 4px;
      }
    }
  }

  .footer {
    margin: 64px 0 20px;
    background-color: transparent;
  }
}
</style>
