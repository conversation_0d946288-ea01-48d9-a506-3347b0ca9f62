<script setup lang="ts">
import CarStepLayout from '@/PageComponents/CarBeforeApplyLoanStep/components/CarStepLayout.vue'
import CarStepOptions from '@/PageComponents/CarBeforeApplyLoanStep/components/CarStepOptions.vue'
import MyNavBar from '@/PageComponents/CarBeforeApplyLoanStep/components/MyNavBar.vue'
import stepBg from '@/assets/images/p2025070201/step_bg2.png'
import titleImg from '@/assets/images/p2025070201/title-1.png'
import { usePageBackInterceptor } from '@/composables/usePageBackInterceptor'
import { TrackClickButtonCarStagingAmountStep } from '@/enum/track'
import { useCarBeforeApplyLoanStep } from '@/composables/useCarBeforeApplyLoanStep'

const pageBackInterceptor = usePageBackInterceptor()

const isBack = ref(false)

const {
  selectCarStagingAmount,
  goBack,
  trackClick,
  status,
  showDialogLeave,
} = useCarBeforeApplyLoanStep({
  trackPage: 'carBeforeApplyLoanAmountStep',
})

const carStatusOptions = [
  { label: '0-10万', value: TrackClickButtonCarStagingAmountStep.ZERO_TO_TEN },
  { label: '10-20万', value: TrackClickButtonCarStagingAmountStep.TEN_TO_TWENTY },
  { label: '20-50万', value: TrackClickButtonCarStagingAmountStep.TWENTY_TO_FIFTY },
  { label: '50-100万', value: TrackClickButtonCarStagingAmountStep.FIFTY_TO_HUNDRED },
]

function handleSelect(value: number) {
  selectCarStagingAmount(value)
  trackClick(value)
}

function handleBack() {
  if (!status.value?.carStagingAmount && !isBack.value) {
    showDialogLeave()
    isBack.value = true
    return false
  }
  return true
}

onMounted(() => {
  pageBackInterceptor.add(handleBack)
})

onBeforeUnmount(() => {
  pageBackInterceptor.remove()
})
</script>

<template>
  <CarStepLayout record="CQ" :step-bg="stepBg">
    <template #header>
      <MyNavBar @back="goBack(), trackClick(TrackClickButtonCarStagingAmountStep.BACK)" />
    </template>

    <CarStepOptions
      v-model="status.carStagingAmount"
      :title-img="titleImg"
      :options="carStatusOptions"
      @select="handleSelect"
    />
  </CarStepLayout>
</template>
