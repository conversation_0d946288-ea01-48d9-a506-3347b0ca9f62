<script setup lang="ts">
// 车牌号 + 车辆状态 正向
import ClueFormForward from '../carBeforeApplyLoanStep/carApplyLoanForwardStep.vue'

// 车牌号 逆向
import ClueFormReverse from '../carBeforeApplyLoanStep/carApplyLoanReverseStep.vue'

// 分发
import DistributionForm from './distribution.vue'
import { PageType } from '@/enum/page'

const route = useRoute()
const pageType = route.query?.pageType
const status = useSessionStorage('CARSTEP_STATUS', {
  isReverse: null,
})
</script>

<template>
  <ClueFormForward v-if="pageType === String(PageType.OUTSIDE_CLUE_PAGE) && !status.isReverse || status.isReverse === 0" />
  <ClueFormReverse v-else-if="pageType === String(PageType.OUTSIDE_CLUE_PAGE) && status.isReverse" />
  <DistributionForm v-else />
</template>
