<script setup lang="ts">
import { useTrack } from '@/composables/useTrack'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { useProtocol } from '@/composables/useProtocol'

defineProps({
  phone: {
    type: [Number || String],
    default: '',
  },
})
defineEmits(['action'])
const { isUnionLogin } = useUnionLogin()
const { showProtocol } = useProtocol({ isInstitutionalAgreementVisible: true, isHidePrivacyPolicyAndUserAgreement: !isUnionLogin.value })

// 1:关闭，2：授权相关协议，3：同意并继续，4：再考虑考虑
enum TrackClickButton {
  CLOSE = 1,
  CONFIRM = 3,
  CANCEL = 4,
  PROTOCOL = 2,
}

const track = useTrack({
  page: 'certificationPop',
  autoShow: true,
  autoStay: true,
})

function trackClick(button: TrackClickButton) {
  track.click({ button })
}
</script>

<template>
  <div class="dialog-form">
    <img
      class="close" src="@/assets/images/home/<USER>"
      @click="$emit('action', { action: 'close' }), trackClick(TrackClickButton.CLOSE)"
    >
    <p class="phone-number">
      尊敬的{{ String(phone).substring(7, 11) }}用户
    </p>
    <div>
      <div class="control mt-120px">
        阅读并同意<span @click.stop="trackClick(TrackClickButton.PROTOCOL);showProtocol(isUnionLogin ? 'privacy-policy' : 'user-share')">《授权相关协议》</span><br>
        为您提供车主专属贷款服务
      </div>
      <button class="confirm-btn cursor-pointer w-264px h-49px mt-20px ml-auto block mr-auto bg-#1677FF text-#FFFFFF text-18px rd-27px" @click="$emit('action', { action: 'confirm' }), trackClick(TrackClickButton.CONFIRM)">
        同意并继续
      </button>
      <button class="cancel-btn cursor-pointer text-#666666 mt-15px text-10px block text-center w-100%" @click="$emit('action', { action: 'cancel' }), trackClick(TrackClickButton.CANCEL)">
        再考虑考虑
      </button>
    </div>
  </div>
</template>

<style lang='less' scoped>
.dialog-form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 15px;
  width: 300px;
  height: 305px;
  position: relative;
  background-image: url(@/assets/images/login/authorize.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .close {
    width: 21px;
    height: 21px;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 999;
  }
  .phone-number {
    padding: 18px 0 0 75px;
    font-weight: 600;
    font-size: 14px;
    color: #2f79ff;
    line-height: 20px;
  }
  .control {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    text-align: center;
    span {
      color: #1677ff;
      cursor: pointer;
    }
  }
  .confirm-btn {
    box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);
  }
}
</style>
