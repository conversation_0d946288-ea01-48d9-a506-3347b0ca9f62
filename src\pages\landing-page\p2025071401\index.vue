<script setup lang="ts">
import { PageType } from '@/enum/page'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import RollingNumber from '@/components/RollingNumber/index.vue'
// 单独引入组件样式
import GaugeEcharts from '@/components/GaugeEcharts/index.vue'
import NoticeBar from '@/components/NoticeBar/index.vue'
import Footer from '@/components/PageFooter/index.vue'
import { useRoute, useRouter } from 'vue-router'
import { useDialog } from '@revfanc/use'
import dialogMount from '@/utils/dialog-mount'
import { useOpenInstall } from '@/composables/useOpenInstall'

const DialogLogin = dialogMount(() => import('./components/DialogLogin/index.vue'))

enum TRACK_BUTTONS {
  GET_QUOTA = 2, // 立即领取额度
}
const TRACK_PAGE = 'landingPage'
const track = useTrack()
const router = useRouter()
const route = useRoute()
const dialog = useDialog()
const { download, install } = useOpenInstall()

function goDownload() {
  download()
  setTimeout(() => {
    router.push({
      path: `${route.path}/download`,
      query: {
        ...route.query,
        pageType: PageType.OUTSIDE_CLUE_PAGE,
      },
    })
  }, 200)
}

function showDialogLogin() {
  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogLogin, {
        onAction: context.callback,
      })
    },
  }).then(goDownload)
}

function trackClick(button: TRACK_BUTTONS) {
  try {
    return track.click({
      page: TRACK_PAGE,
      button,
    })
  }
  catch (error) {
    return error
  }
}
onMounted(() => {
  install()

  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
    })
  })

  track.stay({
    page: TRACK_PAGE,
  })
})
</script>

<template>
  <div class="page-container pt-146px pb-30px">
    <div class="progress-container">
      <GaugeEcharts :tick-inner-distance="20" :tick-outer-distance="16" :circle-r="122" :endpoint-border-width="1" :duration="1300" />
      <div class="rolling-num">
        <div class="gray-text w-100%">
          贷款参考额度
        </div>
        <div class="rolling-num-blue flex w-100% items-center justify-center">
          <RollingNumber :start="150" :target="198" :duration="1300" :increment="1" :delay="0" />000
        </div>
      </div>
    </div>
    <div class="page-button mt-104px mr-auto ml-auto relative">
      <img
        class="w-325px h-61px block mr-auto ml-auto relative z-1"
        src="@/assets/images/2025070801/btn.png"
        @click="showDialogLogin(), trackClick(TRACK_BUTTONS.GET_QUOTA)"
      >
      <img class="absolute top-27px right-6px finger w-50px h-60px" src="@/assets/images/2025070801/finger.png" alt="" srcset="">
    </div>
    <div class="mt-13px w-217px h-23px rd-11px bg-#FFF2CF mr-auto ml-auto">
      <NoticeBar :speed="60" width="199" />
    </div>
    <Footer class="footer" />
  </div>
</template>

  <style lang="less" scoped>
  .page-container {
  width: 100%;
  min-height: 812px;
  background-color: #fff;
  background-image: url('@/assets/images/p2025071401/page1_bg.png');
  background-size: 375px 812px;
  background-repeat: no-repeat;
  font-family:
    PingFangSC,
    PingFang SC;
  display: flex;
  flex-direction: column;
  .progress-container {
    margin: 0 auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .rolling-num {
      position: absolute;
      width: 100%;
      top: 78px;
      text-align: center;
    }
    .gray-text {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 10px;
      color: #999999;
      line-height: 14px;
    }
    .rolling-num-blue {
      font-family: MiSans, MiSans;
      font-weight: 800;
      font-size: 48px;
      color: #1677ff;
      line-height: 64px;
    }
  }
  .finger {
    z-index: 99;
    animation: tap-animation 0.8s infinite ease-in-out;
  }
  @keyframes tap-animation {
    0%,
    100% {
      transform: translate(-50%, 0) scale(1);
      filter: drop-shadow(10px 10px 4px rgba(0, 0, 0, 0.2));
    }
    50% {
      transform: translate(-50%, 10px) scale(0.95);
      filter: drop-shadow(5px 5px 2px rgba(0, 0, 0, 0.4));
    }
  }
  .footer {
    margin-top: 240px;
  }
}
</style>
