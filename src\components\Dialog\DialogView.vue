<script setup lang="ts">
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'

const emit = defineEmits(['action'])
const track = useTrack()

// 埋点按钮值定义
const TRACK_BUTTONS = {
  CLOSE: 1, // 关闭按钮
  VIEW: 2, // 立即查看
} as const

// 埋点页面名称
const TRACK_PAGE = 'downloadWaitPopup'

function handleView() {
  // 点击立即查看按钮埋点
  track.click({ page: TRACK_PAGE, button: TRACK_BUTTONS.VIEW })

  emit('action', { action: 'confirm' })
}

function handleClose() {
  // 点击关闭按钮埋点
  track.click({ page: TRACK_PAGE, button: TRACK_BUTTONS.CLOSE })

  emit('action', { action: 'close' })
}

// 监听弹窗显示
track.show({ page: TRACK_PAGE })
</script>

<template>
  <div class="dialog-content" @click.stop>
    <img class="close" src="@/pages/landing-page/p2025040901/images/close_w.png" @click="handleClose">
    <div class="content">
      <button class="btn-view" @click="handleView">
        立即查看
        <img class="tips" src="@/pages/landing-page/p2025040901/images/tips_3.png">
      </button>
    </div>
  </div>
</template>

<style lang="less" scoped>
.dialog-content {
  width: 380px;
  height: 380px;
  background: url('@/pages/landing-page/p2025040901/images/banner_7.png') no-repeat center;
  background-size: contain;
  border-radius: 12px;
  text-align: center;
  display: flex;
  flex-direction: column;

  .close {
    position: absolute;
    top: 66px;
    right: 36px;
    width: 21px;
    height: 21px;
    cursor: pointer;
    z-index: 1;
  }

  .content {
    margin-top: 206px;

    .btn-view {
      position: relative;
      width: 289px;
      height: 48px;
      background: #1677ff;
      border-radius: 9999px;
      color: #ffffff;
      font-size: 18px;
      font-weight: 500;
      margin: 0 auto 0;
      cursor: pointer;
      box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);

      .tips {
        position: absolute;
        top: -20px;
        right: -10px;
        width: 100px;
        height: 24px;
      }
    }
  }
}
</style>
