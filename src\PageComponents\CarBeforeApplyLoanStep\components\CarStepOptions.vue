<script setup lang="ts">
import { ref } from 'vue'

interface Option {
  label: string
  value: number
}

interface Props {
  titleImg: string
  options: Option[]
}

const { titleImg, options } = defineProps<Props>()
const emit = defineEmits(['select'])
const modelValue = defineModel<number>()

const isSelectionDisabled = ref(false)

function handleSelect(value: number) {
  if (isSelectionDisabled.value)
    return

  modelValue.value = value
  isSelectionDisabled.value = true

  emit('select', value)
}
</script>

<template>
  <Transition name="slide-fade" appear>
    <div class="options-container">
      <img class="title-img" :src="titleImg">
      <div class="options">
        <button
          v-for="option in options"
          :key="option.value"
          class="option-button"
          :class="{ selected: modelValue === option.value }"
          :disabled="isSelectionDisabled"
          @click="handleSelect(option.value)"
        >
          {{ option.label }}
        </button>
      </div>
      <!-- <div class="desc">
        最终借款额度、借款利率、借款周期以实际审批为准
      </div> -->
    </div>
  </Transition>
</template>

<style scoped lang="less">
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(40px);
  opacity: 0;
}

.options-container {
  margin-top: 10px;
  background: #ffffff;
  border-radius: 8px;
  padding: 50px 20px 20px;
  text-align: center;
  position: relative;
  box-shadow: 0px 0px 3px 0px rgba(0, 49, 118, 0.12);
}

.title-img {
  position: absolute;
  top: 0;
  left: 50%;
  height: 34px;
  transform: translateX(-50%);
}
.options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-button {
  width: 100%;
  height: 50px;
  border-radius: 8px;
  font-weight: 500;
  border: 1px solid #e9e9e9;
  background-color: #f8f8f8;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.1);
  font-size: 18px;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;

  &.selected {
    background-color: #e7f1ff;
    color: #1677ff;
    border-color: #1677ff;
  }

  &:disabled {
    cursor: not-allowed;
  }
}

.desc {
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  margin-top: 15px;
}
</style>
