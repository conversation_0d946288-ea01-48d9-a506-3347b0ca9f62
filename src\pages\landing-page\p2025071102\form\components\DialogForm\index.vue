<script setup lang="ts">
import PlateNumber from '../PlateNumber/index.vue'
import RegionPicker from '@/components/RegionPicker/index.vue'
import CarStatus from '@/components/CarStatus/index.vue'
import IdentityCard from '@/components/IdentityCard/index.vue'

import { useForm } from '@/pages/composables/useForm'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import { ref } from 'vue'

defineProps({
  itemType: {
    type: String,
    default: 'plateNumber', // plateNumber || identityCard
  },
})

defineEmits(['action'])

// 1.关闭 2.一键获取额度 3.所在城市点击 4.车牌号点击 5.车牌号输入完成 6.身份证上传点击 7.身份证上传完成 8.车辆状态点击 9.勾选协议
enum TrackClickButton {
  CLOSE = 1,
  FINISH = 2,
  REGION = 3,
  CAR = 4,
  FINISH_PLATE = 5,
  UPLOAD_ID = 6,
  FINISH_ID = 7,
  CAR_STATUS = 8,
  PROTOCOL = 9,
}

const TRACK_PAGE = 'iosBackInterceptorPopup'
const keyboardFocus = ref(false)

const {
  info,
  region,
  identityCards,
  plateNumberRef,
  plateNumber,
  carStatus,
  goPicture,
  updatePlateNumber,
  updateIdentityCard,
} = useForm()

const track = useTrack()

function trackClick(button) {
  track.click({
    page: TRACK_PAGE,
    button,
  })
}

function uploadTwice() {
  if (identityCards.value.front && identityCards.value.back) {
    trackClick(TrackClickButton.FINISH_ID)
  }
}

onMounted(() => {
  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
    })
  })
})
</script>

<template>
  <div class="dialog-form" :class="{ focus: keyboardFocus }">
    <img
      class="close" src="@/assets/images/home/<USER>"
      @click="$emit('action', { action: 'close' }), trackClick(TrackClickButton.CLOSE)"
    >
    <form class="form" @submit.prevent>
      <template v-if="itemType === 'plateNumber'">
        <div class="form-subtitle">
          补充信息获取额度
        </div>

        <div class="box">
          <div class="box-title flex items-center">
            <img class="car" src="@/pages/landing-page/p2025032401/images/car_1.png">
            <div>车牌号码</div>
          </div>

          <!-- 车牌输入区域 -->
          <PlateNumber
            ref="plateNumberRef" v-model="plateNumber" class="plate" :dialog="true"
            @invoke="trackClick(TrackClickButton.CAR)" @done="trackClick(TrackClickButton.FINISH_PLATE)"
            @focus="keyboardFocus = true" @blur="keyboardFocus = false"
          />

          <div class="line" />

          <!-- 地区选择 -->
          <RegionPicker v-model="region" @invoke="trackClick(TrackClickButton.REGION)" />

          <div class="line" />

          <!-- 车辆状态 -->
          <CarStatus v-model="carStatus" @change="trackClick(TrackClickButton.CAR_STATUS)" />
        </div>

        <button
          class="submit-btn bounce"
          @click="updatePlateNumber().then(() => { $emit('action', { action: 'confirm' }) }), trackClick(TrackClickButton.FINISH)"
        >
          一键获取额度
          <img class="hand" src="@/assets/images/home/<USER>">
        </button>
        <img class="desc" src="@/pages/landing-page/p2025032401/images/text_1.png">
      </template>

      <template v-else-if="itemType === 'identityCard'">
        <!-- 上传身份证 -->
        <IdentityCard
          class="identity-card" :info="info" :identity-cards="identityCards" style-type="dialog"
          @picture="goPicture($event).finally(() => { uploadTwice() }), trackClick(TrackClickButton.UPLOAD_ID)"
        />
        <button
          class="submit-btn bounce"
          @click="updateIdentityCard().then(() => { $emit('action', { action: 'confirm' }) }), trackClick(TrackClickButton.FINISH)"
        >
          立即提额
          <img class="hand" src="@/assets/images/home/<USER>">
        </button>
      </template>
    </form>
  </div>
</template>

<style lang='less' scoped>
.dialog-form {
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.2s;
  width: 375px;
  height: auto;
  background-image: url('@/pages/landing-page/p2025040901/images/popup3_bg.png');
  background-size: 367px 332px;
  background-position: 7px 0;
  background-repeat: no-repeat;

  &.focus {
    padding-bottom: 100px;
  }

  .close {
    width: 21px;
    height: 21px;
    position: absolute;
    right: 44px;
    top: -2px;
    z-index: 3;
  }

  .form {
    position: relative;
    z-index: 2;
    width: 351px;
    border-radius: 12px 12px 0 0;
    margin: 0 auto;
    padding-bottom: 20px;
    display: flex;
    flex-direction: column;

    .form-header {
      width: 321px;
      height: 52px;
      background: #f7f8f9;
      border-radius: 9999px;
      margin: 15px auto 0;

      .phone {
        width: 17px;
        height: 20px;
        margin: 0 10px 0 15px;
      }

      .value {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: #999999;
        line-height: 22px;
      }

      .status {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #70d300;
        line-height: 20px;
        margin-left: auto;
      }

      .icon {
        width: 18px;
        height: 18px;
        margin: 0 15px 0 5px;
      }
    }

    .form-subtitle {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #1677ff;
      line-height: 20px;
      position: relative;
      margin: 15px auto 0;

      &::before {
        content: '';
        width: 16px;
        height: 2px;
        background: linear-gradient(90deg, rgba(22, 119, 255, 0) 0%, #1677ff 100%);
        border-radius: 1px;
        position: absolute;
        left: -21px;
        top: 50%;
        transform: translateY(-50%);
      }

      &::after {
        content: '';
        width: 16px;
        height: 2px;
        background: linear-gradient(90deg, #1677ff 0%, rgba(22, 119, 255, 0) 100%);
        border-radius: 1px;
        position: absolute;
        right: -21px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .box {
      width: 331px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #cccccc;
      margin: 15px auto 0;
      padding-bottom: 10px;

      .box-title {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin: 10px 10px 0;

        .car {
          width: 20px;
          height: 20px;
          margin-right: 5px;
        }
      }

      .plate {
        margin: 10px auto 0;
      }

      .line {
        width: 311px;
        height: 1px;
        background: #eeeeee;
        border-radius: 5px;
        margin: 10px auto 0;
      }
    }

    .identity-card {
      margin: 117px auto 0;
    }

    .submit-btn {
      display: block;
      width: 265px;
      height: 50px;
      background: #1677ff;
      box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);
      border-radius: 9999px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #ffffff;
      line-height: 25px;
      margin: 15px auto 0;
      position: relative;

      .hand {
        width: 50px;
        height: 60px;
        position: absolute;
        right: -11px;
        bottom: -30px;
      }
    }

    .desc {
      width: 157px;
      height: 16px;
      display: block;
      margin: 20px auto 0;
    }
  }
}
</style>
