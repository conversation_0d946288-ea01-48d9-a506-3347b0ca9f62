<script setup lang="ts">
import NavBar from '@/components/NavBar/index.vue'
import AssessStatus from './components/AssessStatus/index.vue'
import PageFooter from '@/components/PageFooter/index.vue'
import PageFooter2 from '@/components/PageFooter/PageFooter2.vue'

import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'

const TRACK_PAGE = 'credentialStuffingFailurePage'

const track = useTrack()

// 页面展示 - 埋点
function trackShow() {
  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
      extendJson: {
        pageType: 1,
      },
    })
  })
}

onMounted(() => {
  trackShow()
})
</script>

<template>
  <div class="assess-page">
    <NavBar :background-custom-style="{ backgroundColor: '#fff' }" />

    <AssessStatus />

    <PageFooter style="margin-bottom: 40px;" company-name="杭州优易润科技有限公司" icp="浙ICP备2025148745号-3" phone="4008602166" />
    <PageFooter2 style="margin-bottom: 40px;" />
  </div>
</template>

<style lang='less' scoped>
.assess-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f9ff;
  background: linear-gradient(181deg, #1677ff 0%, #1677ff 100px, #f8faff 400px, #f8faff 100%);

  .icon-back {
    width: 9px;
    height: 15px;
    margin-left: 14px;
    margin-right: 8px;
    color: #000;
  }

  .nav-title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 17px;
    color: #000;
    line-height: 24px;
  }

  .footer {
    margin-top: auto;
    margin-bottom: 30px;
  }
}
</style>
