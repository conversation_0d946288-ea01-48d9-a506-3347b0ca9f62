import { computed, ref, watch } from 'vue'
import type { ComputedRef } from 'vue'
import { useTrack } from '@/composables/useTrack'
import { debounce } from 'lodash-es'

// 防抖状态管理 - 防止表单完成事件重复触发
const formCompleteDebounce = debounce((fn) => {
  if (typeof fn === 'function') {
    fn()
  }
}, 1000)

// 全局表单数据状态管理
const globalFormFields = ref<FormFieldState>({
  plateNumber: '',
  region: {
    cityId: null,
    cityName: null,
    provinceId: null,
    provinceName: null,
  },
  carStatus: 1,
  identityCards: {
    front: '',
    back: '',
  },
  realName: '',
  phoneNumber: '',
  verificationCode: '',
})

// 更新全局表单数据的函数
export function updateGlobalFormFields(fields: Partial<FormFieldState>) {
  Object.assign(globalFormFields.value, fields)

  Object.keys(globalFormFields.value).forEach((key) => {
    const isEmptyKey = !Object.prototype.hasOwnProperty.call(fields, key)

    if (key === 'identityCards' && isEmptyKey && !globalFormFields.value[key].front && !globalFormFields.value[key].back) {
      delete globalFormFields.value.identityCards
      return
    }

    if (key === 'region' && isEmptyKey && !globalFormFields.value[key].cityId && !globalFormFields.value[key].cityName && !globalFormFields.value[key].provinceId && !globalFormFields.value[key].provinceName) {
      delete globalFormFields.value.region
      return
    }

    if (isEmptyKey && !globalFormFields.value[key]) {
      delete globalFormFields.value[key]
    }
  })
}

// 获取全局表单数据的函数
export function getGlobalFormFields() {
  return globalFormFields
}

// 重置全局表单数据的函数
export function resetGlobalFormFields() {
  globalFormFields.value = {
    plateNumber: '',
    region: {
      cityId: null,
      cityName: null,
      provinceId: null,
      provinceName: null,
    },
    carStatus: 1,
    identityCards: {
      front: '',
      back: '',
    },
    realName: '',
    phoneNumber: '',
    verificationCode: '',
  }
}

// 表单项输入埋点枚举
export enum FormInputTrackEvent {
  // 输入开始事件 (1xx)
  PLATE_NUMBER_START = 101,
  REGION_START = 102,
  CAR_STATUS_START = 103,
  IDENTITY_CARDS_START = 104,
  REAL_NAME_START = 105,
  PHONE_NUMBER_START = 106,
  VERIFICATION_CODE_START = 107,

  // 输入完成事件 (2xx)
  PLATE_NUMBER_COMPLETE = 201,
  REGION_COMPLETE = 202,
  CAR_STATUS_COMPLETE = 203,
  IDENTITY_CARDS_COMPLETE = 204,
  REAL_NAME_COMPLETE = 205,
  PHONE_NUMBER_COMPLETE = 206,
  VERIFICATION_CODE_COMPLETE = 207,

  // 表单整体完成事件 (3xx)
  FORM_ALL_COMPLETE = 301,
}

// 表单项状态接口 - 所有字段都是可选的
export interface FormFieldState {
  plateNumber?: string
  region?: {
    cityId: number | null
    cityName: string | null
    provinceId: number | null
    provinceName: string | null
  }
  carStatus?: number
  identityCards?: {
    front: string
    back: string
  }
  realName?: string
  phoneNumber?: string
  verificationCode?: string
}

// 表单项完成状态检查函数
export interface FormValidationRules {
  plateNumber: (value: string) => boolean
  region: (value: FormFieldState['region']) => boolean
  carStatus: (value: number) => boolean
  identityCards: (value: FormFieldState['identityCards']) => boolean
  realName: (value: string) => boolean
  phoneNumber?: (value: string) => boolean
  verificationCode?: (value: string) => boolean
}

// 默认验证规则
export const defaultValidationRules: FormValidationRules = {
  plateNumber: (value: string) => value && value.length >= 7,
  region: (value: FormFieldState['region']) => !!(value.cityId && value.cityName && value.provinceId && value.provinceName),
  carStatus: (value: number) => value > 0,
  identityCards: (value: FormFieldState['identityCards']) => !!(value.front && value.back),
  realName: (value: string) => value && value.trim().length > 0,
  phoneNumber: (value: string) => value && value.length === 11,
  verificationCode: (value: string) => value && value.length >= 4,
}

export interface UseFormTrackingOptions {
  page: string
  formFields: ComputedRef<FormFieldState>
  validationRules?: Partial<FormValidationRules>
  enableFormCompleteTracking?: boolean
  syncToGlobal?: boolean // 是否同步表单数据到全局状态，用于页面和弹窗间共享数据
}

/**
 * 表单埋点 Hook
 * 统一管理表单项的输入开始、输入完成和整体完成埋点
 */
export function useFormTracking(options: UseFormTrackingOptions) {
  const {
    page,
    formFields,
    validationRules = {},
    enableFormCompleteTracking = true,
    syncToGlobal = true,
  } = options

  const track = useTrack({ page })
  const rules = { ...defaultValidationRules, ...validationRules }

  // 如果启用了全局同步，监听表单数据变化并同步到全局状态
  if (syncToGlobal) {
    watch(
      formFields,
      (newFields) => {
        updateGlobalFormFields(newFields)
      },
      { deep: true, immediate: true },
    )
  }

  // 埋点上报函数
  function trackFormEvent(event: FormInputTrackEvent, _fieldName?: string) {
    try {
      console.log(`trackInput${event}`, _fieldName)
      track.click({
        button: event,
        extendJson: {
          formFields: globalFormFields.value,
        },
      })
    }
    catch (error) {
      console.warn('Form tracking error:', error)
    }
  }

  // 检查表单整体完成状态 - 基于计算属性的当前值
  const checkFormComplete = computed(() => {
    const currentFields = globalFormFields.value

    // 遍历当前存在的字段进行验证
    for (const [fieldName, fieldValue] of Object.entries(currentFields)) {
      // 跳过 undefined 的字段
      if (fieldValue === undefined) {
        continue
      }

      // 获取对应的验证规则
      const validator = rules[fieldName as keyof FormValidationRules] as ((value: any) => boolean) | undefined

      // 如果有验证规则且验证失败，返回 false
      if (validator && !validator(fieldValue)) {
        return false
      }
    }

    return true
  })

  // 字段配置 - 定义字段名到事件和状态的映射
  const fieldConfigs = {
    plateNumber: {
      startEvent: FormInputTrackEvent.PLATE_NUMBER_START,
      completeEvent: FormInputTrackEvent.PLATE_NUMBER_COMPLETE,
    },
    region: {
      startEvent: FormInputTrackEvent.REGION_START,
      completeEvent: FormInputTrackEvent.REGION_COMPLETE,
    },
    carStatus: {
      startEvent: FormInputTrackEvent.CAR_STATUS_START,
      completeEvent: FormInputTrackEvent.CAR_STATUS_COMPLETE,
    },
    identityCards: {
      startEvent: FormInputTrackEvent.IDENTITY_CARDS_START,
      completeEvent: FormInputTrackEvent.IDENTITY_CARDS_COMPLETE,
    },
    realName: {
      startEvent: FormInputTrackEvent.REAL_NAME_START,
      completeEvent: FormInputTrackEvent.REAL_NAME_COMPLETE,
    },
    phoneNumber: {
      startEvent: FormInputTrackEvent.PHONE_NUMBER_START,
      completeEvent: FormInputTrackEvent.PHONE_NUMBER_COMPLETE,
    },
    verificationCode: {
      startEvent: FormInputTrackEvent.VERIFICATION_CODE_START,
      completeEvent: FormInputTrackEvent.VERIFICATION_CODE_COMPLETE,
    },
  } as const

  // 通用的埋点创建函数
  const createTrackingFunction = (type: 'start' | 'complete') => (fieldName: string) => {
    const config = fieldConfigs[fieldName as keyof typeof fieldConfigs]

    // 校验值是否满足条件
    if (type === 'complete') {
      const fieldValue = formFields.value[fieldName as keyof FormFieldState]
      const validator = rules[fieldName as keyof FormValidationRules] as ((value: any) => boolean) | undefined
      if (validator && !validator(fieldValue)) {
        return
      }
    }

    if (config) {
      const event = type === 'start' ? config.startEvent : config.completeEvent

      trackFormEvent(event, fieldName)
    }
  }

  // 创建输入开始埋点函数
  const trackInputStart = createTrackingFunction('start')

  // 创建输入完成埋点函数
  const trackInputComplete = createTrackingFunction('complete')

  // 监听表单整体完成状态 - 监听计算属性的变化
  if (enableFormCompleteTracking) {
    watch(
      [globalFormFields, checkFormComplete],
      ([_currentFields, isComplete]) => {
        formCompleteDebounce(() => {
          if (isComplete) {
            const event = FormInputTrackEvent.FORM_ALL_COMPLETE
            trackFormEvent(event, 'formComplete')
          }
        })
      },
      { deep: true },
    )
  }

  return {
    trackInputStart,
    trackInputComplete,
    trackFormEvent,
    checkFormComplete,
  }
}
