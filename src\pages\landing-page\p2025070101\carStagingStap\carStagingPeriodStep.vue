<script setup lang="ts">
import CarStagingStapLayout from '@/PageComponents/CarStagingStap/components/CarStagingStapLayout.vue'
import CarStagingStapOptions from '@/PageComponents/CarStagingStap/components/CarStagingStapOptions.vue'
import MyNavBar from '@/PageComponents/CarStagingStap/components/MyNavBar.vue'
import { TrackClickButtonCarStagingPeriodStep } from '@/enum/track'
import { useCarStagingStap } from '@/composables/useCarStagingStap'
import TopContainer from '@/PageComponents/CarStagingStap/components/TopContainer.vue'
import titleImg from '@/assets/images/2025062601/title-2.png'

const {
  selectCarStagingPeriod,
  goBack,
  trackClick,
  status,
} = useCarStagingStap({
  trackPage: 'carStagingPeriodStep',
})

const carStagingPeriodOptions = [
  { label: '36期', value: TrackClickButtonCarStagingPeriodStep.THIRTY_SIX },
  { label: '24期', value: TrackClickButtonCarStagingPeriodStep.TWENTY_FOUR },
  { label: '12期', value: TrackClickButtonCarStagingPeriodStep.TWELVE },
  { label: '6期', value: TrackClickButtonCarStagingPeriodStep.SIX },
]

function handleSelect(value: number) {
  selectCarStagingPeriod(value)
  trackClick(value)
}
</script>

<template>
  <CarStagingStapLayout record="HZ" footer-tips>
    <template #header>
      <MyNavBar @back="goBack(), trackClick(TrackClickButtonCarStagingPeriodStep.BACK)" />
    </template>

    <TopContainer />

    <CarStagingStapOptions
      v-model="status.carStagingPeriod"
      :title-img="titleImg"
      :options="carStagingPeriodOptions"
      @select="handleSelect"
    />
  </CarStagingStapLayout>
</template>
