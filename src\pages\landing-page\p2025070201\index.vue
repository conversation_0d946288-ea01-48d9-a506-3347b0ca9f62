<script setup lang="ts">
import JointLogin from '@/PageComponents/Login/JointLogin.vue'
</script>

<template>
  <JointLogin link-type="form" />
</template>

<style scoped lang="less">
.home-page {
  width: 375px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ecf5fc;

  .nav-bar {
    .back {
      width: 9px;
      height: 15px;
      margin-left: 14px;
    }
  }

  .header {
    height: 224px;
    position: relative;

    .logo-text {
      width: 93px;
      height: 22px;
      position: absolute;
      top: 26px;
      left: 14px;
      z-index: 1;
    }

    .banner {
      margin-top: -10px;
    }
  }

  .banner2 {
    height: 257px;
    background-size: 100% 100%;
    background-image: url('@/pages/landing-page/p2025040901/images/banner.png');
    margin-top: -70px;
    position: relative;

    .amount {
      position: absolute;
      top: 74px;
      left: 50%;
      transform: translateX(-50%);
      white-space: nowrap;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 48px;
      color: #ffffff;
      line-height: 67px;
    }
  }

  .form {
    width: 345px;
    background: #ffffff;
    box-shadow: 0px 0px 6px 0px rgba(0, 49, 118, 0.19);
    border-radius: 0px 0px 16px 16px;
    display: flex;
    flex-direction: column;
    margin: -1px auto 0;

    .form-item {
      margin: 15px auto 0;
      display: flex;
      align-items: center;
      width: 321px;
      height: 52px;

      .form-input {
        width: auto;
        flex: 1;
        display: flex;
        align-items: center;
        height: 52px;
        background: #f7f8f9;
        border-radius: 9999px;
        border: 1px solid rgba(22, 119, 255, 0.2);

        .icon {
          width: 20px;
          height: 20px;
          margin: 0 10px 0 15px;
          object-fit: contain;
        }

        input {
          width: 100px;
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 22px;
        }
      }

      .form-sms {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        white-space: nowrap;
        margin: 0 5px 0 15px;

        &:disabled {
          color: #999999;
        }
      }
    }

    .submit {
      width: 331px;
      height: 61px;
      display: block;
      margin: 15px auto 0;
      background-image: url('@/pages/landing-page/p2025040901/images/btn.png');
      background-size: 100% 100%;
    }

    .protocol {
      margin: 8px 19px 15px;
      display: flex;

      .icon {
        width: 12px;
        height: 12px;
        margin-right: 5px;
        display: inline-block;
      }

      .text {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 10px;
        color: #666666;
        line-height: 14px;

        a {
          text-decoration: underline;
        }
      }
    }
  }

  .footer {
    margin: 30px 0 20px;
  }
}
</style>
