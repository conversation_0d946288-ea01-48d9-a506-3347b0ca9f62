<script lang="ts" setup>
import { PROVINCES } from '@/constants'

const props = defineProps({
  speed: {
    type: Number,
    default: 60,
  },
  width: {
    type: String,
    default: '199',
  },
  backgroundColor: {
    type: String,
    default: '#FFF2CF',
  },
  color: {
    type: String,
    default: '#A85D18',
  },
})
const list = Array.from({ length: 20 }).fill('0').map(() => {
  const region = PROVINCES[Math.floor(Math.random() * PROVINCES.length)] // 随机
  const letter = ['A', 'B', 'C', 'D', 'E'][Math.floor(Math.random() * 5)] // 字母 A-E随机
  const number = Math.floor(Math.random() * 10) // 数字 0-9随机
  const name = `车牌${region}${letter}****${number}借款成功`
  const quota = Math.floor(Math.random() * 90 + 10) // 10-100随机整数
  return {
    name,
    quota, // 额度
  }
})
const plateNumber = ref(list)
const noticeRef = ref<HTMLElement>()
onMounted(() => {
  const scrollDuration = (noticeRef.value?.scrollWidth / props.speed) || 10
  noticeRef.value?.style.setProperty('--scroll-duration', `${scrollDuration.toFixed(2)}s`)
})
</script>

<template>
  <div class="flex flex-no-wrap items-center van-notice-bar-custom" :class="[`bg-${props.backgroundColor}`]" :style="{ color: props.color }">
    <slot name="left-icon">
      <img class="w-15px h-15px mr-5px" src="@/assets/images/2025070801/icon1.png" alt="" srcset="">
    </slot>
    <div class="overflow-hidden" :class="[`w-${props.width}px`]">
      <div ref="noticeRef" class="scroll-text flex flex-no-wrap">
        <div v-for="item2 in 2" :key="item2" class="scroll-text-container flex flex-no-wrap items-center">
          <p v-for="(item, index) in plateNumber" :key="index" class="whitespace-nowrap mr-5px text-12px">
            {{ item.name }}<span class="orange">{{ item.quota }}万</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .van-notice-bar-custom {
  height: 100%;
  border-radius: 11px;
  padding-left: 6px;
  padding-right: 0;
  width: 100%;
  .scroll-text {
    display: flex;
    width: fit-content;
    animation: scroll-left var(--scroll-duration, 10s) linear infinite;
  }
  .scroll-text-container {
    flex-shrink: 0;
  }
  @keyframes scroll-left {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }
}
</style>
