<script setup lang="ts">
import { useSmsLogin } from '@/composables/useSmsLogin'
import { useProtocol } from '@/composables/useProtocol'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'

const emit = defineEmits(['action'])

const {
  phoneNumber,
  phoneNumberValid,
  phoneInputRef,
  verificationCode,
  codeInputRef,
  verificationBtnText,
  verificationBtnDisabled,
  readProtocol,
  handleSms,
  handleLogin,
  agreementDialogConfirmTime,
  handleAgreementClick,
} = useSmsLogin({ isIndex: true })
const { showProtocol } = useProtocol()

// 1.关闭 2.一键获取额度 3.勾选协议 4.手机号输入点击 5.手机号输入完成 6.验证码获取 7.验证码输入完成
enum TRACK_BUTTONS {
  CLOSE = 1, // 关闭按钮
  GET_QUOTA = 2, // 一键获取额度
  PROTOCOL = 3, // 勾选协议
  PHONE_INPUT_CLICK = 4, // 手机号输入点击
  PHONE_INPUT_FINISH = 5, // 手机号输入完成
  CODE_INPUT_CLICK = 6, // 获取验证码
  CODE_INPUT_FINISH = 7, // 验证码输入完成
}

const TRACK_PAGE = 'doNotingPopup'

const track = useTrack()

watch(() => phoneNumber.value, (val) => {
  if (val.length === 11) {
    trackClick(TRACK_BUTTONS.PHONE_INPUT_FINISH)
  }
})

watch(() => verificationCode.value, (val) => {
  if (val.length === 4) {
    trackClick(TRACK_BUTTONS.CODE_INPUT_FINISH)
  }
})

function trackClick(button: TRACK_BUTTONS) {
  try {
    return track.click({
      page: TRACK_PAGE,
      button,
    })
  }
  catch (error) {
    return error
  }
}

function onSuccess() {
  emit('action', { action: 'success' })
}

function handleSubmit() {
  agreementDialogConfirmTime.value = Date.now()
  handleLogin().then(res => res && onSuccess())
}

onMounted(() => {
  track.show({ page: TRACK_PAGE })
})
</script>

<template>
  <div class="dialog">
    <img
      class="close" src="@/assets/images/home/<USER>"
      @click="emit('action', { action: 'close' }), trackClick(TRACK_BUTTONS.CLOSE)"
    >
    <div class="title">
      填写手机号获取额度
    </div>
    <form
      class="form"
      @submit.prevent
    >
      <div class="form-item">
        <div class="form-input">
          <img v-if="phoneNumberValid" class="icon" src="@/pages/landing-page/p2025040901/images/phone.png">
          <img v-else class="icon" src="@/pages/landing-page/p2025040901/images/phone_2.png">
          <input
            ref="phoneInputRef" v-model="phoneNumber" type="tel" placeholder="请输入领取手机号" maxlength="11"
            @click="trackClick(TRACK_BUTTONS.PHONE_INPUT_CLICK)"
          >
        </div>
      </div>
      <div class="form-item">
        <div class="form-input">
          <img class="icon" src="@/pages/landing-page/p2025040901/images/code.png">
          <input ref="codeInputRef" v-model="verificationCode" type="tel" placeholder="请输入验证码" maxlength="4">
        </div>
        <button
          class="form-sms" type="button" :disabled="verificationBtnDisabled"
          @click="handleSms(), trackClick(TRACK_BUTTONS.CODE_INPUT_CLICK)"
        >
          {{ verificationBtnText }}
        </button>
      </div>
      <button class="submit" type="button" @click="handleSubmit(), trackClick(TRACK_BUTTONS.GET_QUOTA)">
        一键获取额度
        <img class="tips" src="@/pages/landing-page/p2025040901/images/tips_1.png">
      </button>

      <div class="protocol">
        <IconCheck
          class="icon" :style="{ color: readProtocol ? '#2F79FF' : '#999999' }"
          @click="(readProtocol = !readProtocol), handleAgreementClick(), trackClick(TRACK_BUTTONS.PROTOCOL)"
        />
        <span class="text">
          我已阅读并同意<a @click="showProtocol('privacy-policy')">《隐私政策》</a><a
            @click="showProtocol('user-agreement')"
          >《用户注册服务协议》</a><a @click="showProtocol('user-share')">《个人信息共享授权协议》</a>
        </span>
      </div>
    </form>
  </div>
</template>

<style lang='less' scoped>
.dialog {
  width: 375px;
  height: 469px;
  background: url('@/pages/landing-page/p2025040901/images/banner_1.png') no-repeat top center;
  background-size: 100% auto;
  display: flex;
  flex-direction: column;

  .close {
    position: absolute;
    top: 69px;
    right: 10px;
    width: 21px;
    height: 21px;
    cursor: pointer;
  }

  .title {
    margin: 188px auto 0;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
  }

  .form {
    width: 335px;
    display: flex;
    flex-direction: column;
    margin: 0 auto 0;

    .form-item {
      margin: 10px auto 0;
      display: flex;
      align-items: center;
      width: 335px;
      height: 52px;

      .form-input {
        width: auto;
        flex: 1;
        display: flex;
        align-items: center;
        width: 335px;
        height: 52px;
        background: #f7f8f9;
        border-radius: 9999px;

        .icon {
          width: 20px;
          height: 20px;
          margin: 0 10px 0 15px;
          object-fit: contain;
        }

        input {
          width: 100px;
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 22px;
        }
      }

      .form-sms {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        white-space: nowrap;
        margin: 0 5px 0 15px;

        &:disabled {
          color: #999999;
        }
      }
    }

    .submit {
      width: 335px;
      height: 49px;
      background: #1677ff;
      box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);
      border-radius: 9999px;
      display: block;
      margin: 15px auto 0;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #ffffff;
      line-height: 25px;
      position: relative;

      .tips {
        position: absolute;
        top: -11px;
        right: 1px;
        width: 122px;
        height: 22px;
      }
    }

    .protocol {
      margin: 8px 19px 15px;
      display: flex;

      .icon {
        width: 14px;
        height: 14px;
        margin-right: 5px;
        margin-top: 2px;
      }

      .text {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #666666;
        line-height: 17px;

        a {
          color: #1677ff;
        }
      }
    }
  }
}
</style>
