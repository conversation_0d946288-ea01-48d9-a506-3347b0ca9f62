<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from 'vue'

export interface Step {
  nodeStatus: 'completed' | 'pending'
  lineStatus: 'completed' | 'pending'
  node?: {
    animationDuration?: number
  }
  line?: {
    text?: string
    animationDuration?: number
  }
}

const props = defineProps<{
  steps: Step[]
  animationType: 'full' | 'partial' | 'special_case' | 'none'
  lineHeights?: number[]
}>()

const internalSteps = ref<Array<{
  nodeAnimated: boolean
  lineAnimated: boolean
  textAnimated: boolean
}>>([])

// 此标志在初始动画后设置为 true，用于禁用所有未来的 CSS 过渡效果。
const animationHasRun = ref(false)
const showNode3FixOverlay = ref(false)

function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 'full' 序列（3个步骤）的硬编码动画。
async function playFullAnimation(finalSteps: Step[]) {
  internalSteps.value = props.steps.map(() => ({ nodeAnimated: false, lineAnimated: false, textAnimated: false }))
  await nextTick()

  // 步骤 1
  if (finalSteps[0]?.nodeStatus === 'completed') {
    internalSteps.value[0].nodeAnimated = true
    await sleep(200)
  }
  if (finalSteps[0]?.lineStatus === 'completed') {
    internalSteps.value[0].lineAnimated = true
    await sleep(500)
  }

  // 步骤 2
  if (finalSteps[1]?.nodeStatus === 'completed') {
    internalSteps.value[1].nodeAnimated = true
    await sleep(200)
  }
  if (finalSteps[1]?.lineStatus === 'completed') {
    internalSteps.value[1].lineAnimated = true
    await sleep(500)
  }
  if (finalSteps[1]?.line?.text) {
    internalSteps.value[1].textAnimated = true
    await sleep(200) // 标签动画
  }

  // 步骤 3
  if (finalSteps[2]?.nodeStatus === 'completed') {
    internalSteps.value[2].nodeAnimated = true
    await sleep(200)
  }

  animationHasRun.value = true
}

showNode3FixOverlay.value = true
setTimeout(() => {
  showNode3FixOverlay.value = false
}, 700)
// 'partial' 序列（2个步骤）的硬编码动画。
async function playPartialAnimation(finalSteps: Step[]) {
  internalSteps.value = props.steps.map(() => ({ nodeAnimated: false, lineAnimated: false, textAnimated: false }))
  await nextTick()

  // 步骤 1
  if (finalSteps[0]?.nodeStatus === 'completed') {
    internalSteps.value[0].nodeAnimated = true
    await sleep(200)
  }
  if (finalSteps[0]?.lineStatus === 'completed') {
    internalSteps.value[0].lineAnimated = true
    await sleep(500)
  }

  // 步骤 2
  if (finalSteps[1]?.nodeStatus === 'completed') {
    internalSteps.value[1].nodeAnimated = true
    await sleep(200)
  }
  if (finalSteps[1]?.lineStatus === 'completed') {
    internalSteps.value[1].lineAnimated = true
    await sleep(500)
  }
  if (finalSteps[1]?.line?.text) {
    internalSteps.value[1].textAnimated = true
    await sleep(200) // 标签动画
  }

  animationHasRun.value = true
}

// 'special_case' (!realName, carStatus, plateNumber) 的硬编码动画。
async function playSpecialAnimation(finalSteps: Step[]) {
  internalSteps.value = props.steps.map(() => ({ nodeAnimated: false, lineAnimated: false, textAnimated: false }))
  await nextTick()

  // 步骤 1: 仅动画化线1。
  if (finalSteps[0]?.lineStatus === 'completed') {
    internalSteps.value[0].lineAnimated = true
    await sleep(500)
  }

  // 步骤 2
  if (finalSteps[1]?.nodeStatus === 'completed') {
    internalSteps.value[1].nodeAnimated = true
    await sleep(500)
  }
  if (finalSteps[1]?.line?.text) {
    internalSteps.value[1].textAnimated = true
    await sleep(200)
  }
  if (finalSteps[1]?.lineStatus === 'completed') {
    internalSteps.value[1].lineAnimated = true
    await sleep(500)
  }

  // 步骤 3
  if (finalSteps[2]?.nodeStatus === 'completed') {
    internalSteps.value[2].nodeAnimated = true
    await sleep(200)
  }

  animationHasRun.value = true
}

async function playNoneAnimation(finalSteps: Step[]) {
  internalSteps.value = props.steps.map(() => ({ nodeAnimated: false, lineAnimated: false, textAnimated: false }))
  await nextTick()

  // 步骤 1: 节点1 画线。
  if (finalSteps[0]?.lineStatus === 'completed') {
    internalSteps.value[0].lineAnimated = true
    await sleep(500)
  }

  // 步骤 2: 节点2 勾选。
  if (finalSteps[1]?.nodeStatus === 'completed') {
    internalSteps.value[1].nodeAnimated = true
    await sleep(200)
  }

  // 步骤 3: 节点2 画线。
  if (finalSteps[1]?.nodeStatus === 'completed') {
    internalSteps.value[1].lineAnimated = true
    await sleep(500)
  }

  animationHasRun.value = true
}

watch(
  () => [props.steps, props.animationType] as const,
  async ([newSteps, newType]) => {
    console.log('>>>>>>', newSteps, newType, animationHasRun.value)
    if (animationHasRun.value) {
      // 如果动画已运行，则直接更新状态。
      internalSteps.value = newSteps.map(step => ({
        nodeAnimated: step.nodeStatus === 'completed',
        lineAnimated: step.lineStatus === 'completed',
        textAnimated: step.lineStatus === 'completed' && !!step.line?.text,
      }))
    }
    else {
      if (newType === 'none') {
        return
      }

      if (newSteps[0]?.nodeStatus === 'completed') {
        internalSteps.value[0].nodeAnimated = true
      }

      if (newSteps[2]) {
        if (!internalSteps.value[2]) {
          internalSteps.value = newSteps.map(() => ({ nodeAnimated: false, lineAnimated: false, textAnimated: false }))
        }
        internalSteps.value[2].nodeAnimated = newSteps[2].nodeStatus === 'completed'
      }

      if (newSteps[1]?.nodeStatus === 'completed' && newSteps[0]?.nodeStatus === 'pending' && newSteps[2]?.nodeStatus === 'pending') {
        internalSteps.value = newSteps.map(() => ({
          nodeAnimated: false,
          lineAnimated: false,
          textAnimated: false,
        }))
        internalSteps.value[0].lineAnimated = true
        internalSteps.value[1].nodeAnimated = true
        internalSteps.value[1].lineAnimated = true
        internalSteps.value[1].textAnimated = true
      }
    }
  },
  // { deep: true, immediate: true },
)

onMounted(async () => {
  await sleep(500)

  if (props.animationType !== 'none') {
    if (props.animationType === 'full') {
      playFullAnimation(props.steps)
    }
    else if (props.animationType === 'partial') {
      playPartialAnimation(props.steps)
    }
    else if (props.animationType === 'special_case') {
      playSpecialAnimation(props.steps)
    }
  }
  else {
    playNoneAnimation(props.steps)
  }
})
</script>

<template>
  <div class="step-line-container" :class="{ 'no-transitions': animationHasRun }">
    <div
      v-for="(step, index) in steps"
      :key="index"
      class="step-item"
      :class="{
        'node-completed': internalSteps[index]?.nodeAnimated,
        'line-completed': internalSteps[index]?.lineAnimated,
        'text-active': internalSteps[index]?.textAnimated,
      }"
    >
      <!-- Node -->
      <div class="node-wrapper">
        <img src="@/assets/images/2025070801/checked.png" alt="checked" class="icon-checked">
        <div class="empty-node" />
        <div v-if="index === 2 && showNode3FixOverlay" class="node-fix-overlay" />
      </div>

      <!-- Line -->
      <div
        v-if="index < steps.length - 1"
        class="line-wrapper"
        :style="{ height: lineHeights?.[index] ? `${lineHeights[index]}px` : '100%' }"
      >
        <div class="line-bg" />
        <div class="line-progress" />
        <div v-if="step.line?.text" class="text-node-wrapper">
          <div class="text-node">
            {{ step.line.text }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.step-line-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  // flex: 1;

  &:last-child {
    flex: 0;
  }
}

.node-wrapper {
  position: relative;
  width: 12px;
  height: 12px;
  flex-shrink: 0;

  .icon-checked,
  .empty-node {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: all 0.2s ease-in-out;
  }

  .empty-node {
    width: 12px;
    height: 12px;
    border: 2px solid #cccccc;
    background: #ffffff;
    border-radius: 50%;
    box-sizing: border-box;
    transform: scale(1);
    opacity: 1;
  }

  .icon-checked {
    transform: scale(0);
    opacity: 0;
  }
}

.step-item.node-completed .node-wrapper {
  .empty-node {
    transform: scale(0);
    opacity: 0;
  }
  .icon-checked {
    transform: scale(1);
    opacity: 1;
  }
}

.node-fix-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  width: 12px;
  height: 12px;
  border: 2px solid #cccccc;
  background: #ffffff;
  border-radius: 50%;
  box-sizing: border-box;
}

.line-wrapper {
  position: relative;
  width: 2px;
  flex-grow: 1;

  .line-bg {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #cccccc;
  }

  .line-progress {
    position: absolute;
    left: 0;
    width: 100%;
    background-color: #1677ff;
    height: 0;
    transition: height 0.5s ease-in-out;
  }
}

.step-item.line-completed .line-wrapper .line-progress {
  height: 100%;
}

.text-node-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;

  .text-node {
    background-color: #cccccc;
    color: white;
    padding: 4px 0;
    border-radius: 12px;
    font-size: 10px;
    width: 15px;
    word-wrap: break-word;
    text-align: center;
    transition: background-color 0.1s ease-in-out;
  }
}

.step-item.text-active .text-node-wrapper .text-node {
  background-color: #1677ff;
}

.no-transitions .icon-checked,
.no-transitions .empty-node,
.no-transitions .line-progress,
.no-transitions .text-node {
  transition: none !important;
}
</style>
