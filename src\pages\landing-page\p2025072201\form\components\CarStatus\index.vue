<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Number,
    default: 1,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const statusList = ref([
  {
    id: 1,
    name: '全款车',
  },
  {
    id: 2,
    name: '按揭-已结清',
  },
  {
    id: 3,
    name: '按揭-未结清',
  },
])
</script>

<template>
  <div class="car-status">
    <div
      v-for="(item) in statusList" :key="item.id" class="status" :class="{ active: item.id === props.modelValue }"
      @click="emit('update:modelValue', item.id), emit('change', item.id)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<style lang='less' scoped>
.car-status {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 10px;

  .status {
    width: 103px;
    height: 40px;
    background: #f0f0f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;

    &.active {
      background: rgba(22, 119, 255, 0.1);
      color: #1677ff;
      border: 1px solid #1677ff;
    }
  }
}
</style>
