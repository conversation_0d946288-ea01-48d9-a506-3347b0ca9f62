<script setup lang="ts">
import PlateNumberKeyboard from '@/components/PlateNumberKeyboard/index.vue'
import ArrowBottom from '@/components/Icons/ArrowBottom.vue'

import { onClickOutside } from '@vueuse/core'
import { ref, useTemplateRef } from 'vue'
import bus from '@/utils/bus'

const props = defineProps({
  modelValue: {
    type: String,
    default: '', // 车牌号
  },
})

const emit = defineEmits(['update:modelValue', 'done', 'invoke', 'keydown', 'focus', 'blur'])

const target = useTemplateRef<HTMLElement>('target')

const plateNumberLength = 8
const plateNumberArr = Array.from({ length: plateNumberLength }).fill('')

const showKeyboard = ref(false)
const activeIndex = ref(0)
const plateNumber = ref(plateNumberArr)
const shakeIndex = ref(-1)

const isInput = ref(false) // 是否输入过

watch(
  () => props.modelValue,
  (val) => {
    const arr = val.split('')

    for (let i = 0; i < plateNumber.value.length; i++) {
      plateNumber.value[i] = arr[i] || ''
    }

    const len = arr.filter(item => item).length || 0

    if (len < 8) {
      activeIndex.value = len
    }
  },
  { immediate: true },
)

watch(
  () => showKeyboard.value,
  (val) => {
    if (val) {
      emit('focus')
    }
    else {
      emit('blur')
    }
  },
)

onMounted(() => {
  bus.on('regionChange', (res) => {
    if (res?.provinceShortName && !isInput.value && props.modelValue.length <= 1) {
      plateNumber.value[0] = res.provinceShortName

      activeIndex.value = 1

      emit('update:modelValue', plateNumber.value.join(''))
    }
  })
})

onClickOutside(target, () => {
  showKeyboard.value = false
})

function handleShowKeyboard() {
  showKeyboard.value = true
}

function handleKeyboardConfirm() {
  showKeyboard.value = false
}

function handleKeyboardChange(value: string) {
  if (value === '-') {
    let lastIndex = plateNumber.value.filter(item => item).length - 1

    if (lastIndex === -1) {
      lastIndex = 0
    }

    plateNumber.value[lastIndex] = ''

    activeIndex.value = lastIndex

    emit('update:modelValue', plateNumber.value.join(''))

    isInput.value = true

    trackDone()
    return
  }

  if (activeIndex.value > plateNumberLength - 1) {
    return
  }

  plateNumber.value[activeIndex.value] = value

  if (activeIndex.value < plateNumberLength - 1) {
    activeIndex.value++
  }

  emit('keydown')

  emit('update:modelValue', plateNumber.value.join(''))

  isInput.value = true

  trackDone()
}

function trackDone() {
  const value = plateNumber.value.filter(item => item)?.length || 0
  if (value === 7 || value === 8) {
    emit('done')
  }
}

function handleActiveIndex(index: number) {
  if (index > plateNumber.value.filter(item => item).length) {
    return
  }

  activeIndex.value = index
}

// 抖动
function shake() {
  const index = plateNumber.value.findIndex(item => !item)

  if (index === -1) {
    return
  }

  activeIndex.value = index

  setTimeout(() => {
    shakeIndex.value = index

    // 浏览器震动
    if (window.navigator.vibrate) {
      window.navigator.vibrate(150)
    }

    setTimeout(() => {
      shakeIndex.value = -1
    }, 800)
  }, 0)
}

defineExpose({
  shake,
  handleShowKeyboard,
  showKeyboard,
})
</script>

<template>
  <div ref="target">
    <!-- 车牌输入区域 -->
    <div class="plate-input-wrapper">
      <div class="plate-input" :class="{ active: showKeyboard }" @click="handleShowKeyboard(), $emit('invoke')">
        <div v-for="(item, index) in plateNumber" :key="index" class="flex items-center">
          <div
            :id="`plate-item-${index}`" class="item flex items-center justify-center" :class="{
              active: activeIndex === index,
              entered: item && index === 0,
              last: !item && index === plateNumberLength - 1,
              shake: index === shakeIndex,
            }" @click="handleActiveIndex(index)"
          >
            {{ item }}

            <ArrowBottom v-if="index === 0" class="arrow" />

            <div v-if="index === 7 && !plateNumber[7] && showKeyboard" class="tips shake-5">
              新能源车主需输入最后一位
            </div>
          </div>
          <div v-if="index === 1" class="line" />
        </div>
      </div>
    </div>

    <!-- 车牌键盘 -->
    <PlateNumberKeyboard
      :show="showKeyboard" :active-index="activeIndex" @change="handleKeyboardChange"
      @confirm="handleKeyboardConfirm"
    />
  </div>
</template>

<style scoped lang="less">
.plate-input-wrapper {
  .plate-input {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;

    .item {
      box-sizing: border-box;
      width: 32px;
      height: 39px;
      background: #f7f7f7;
      border-radius: 8px;
      margin: 0 2.5px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #333;
      line-height: 25px;
      position: relative;

      &.active {
        border: 1px solid #1677ff;
      }

      // 已经输入
      &.entered {
        background: #1677ff;
        color: #fff;
      }

      &.last {
        background-image: url('@/pages/landing-page/p2025032401/images/new_energy.png');
        background-size: 100% 100%;
      }

      .arrow {
        width: 8px;
        height: 6px;
        position: absolute;
        left: 50%;
        bottom: 3px;
        transform: translateX(-50%);
      }

      .tips {
        background: rgba(0, 0, 0, 0.8);
        border-radius: 8px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 12px;
        color: #ffffff;
        line-height: 17px;
        position: absolute;
        right: -10px;
        bottom: calc(100% + 10px);
        padding: 2px 6px;
        white-space: nowrap;

        &::after {
          content: '';
          position: absolute;
          top: calc(100% - 1px);
          right: 20px;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
        }
      }
    }

    .line {
      width: 13px;
      height: 3px;
      background: #eeeeee;
      border-radius: 2px;
      margin: 0 3px;
    }
  }
}

.shake {
  animation: shake 0.2s ease-in-out infinite;
}

.shake-5 {
  animation: shake 0.5s ease-in-out infinite;
}

@keyframes shake {
  0% {
    transform: translateY(0);
  }

  25% {
    transform: translateY(2px);
  }

  50% {
    transform: translateY(0px);
  }

  75% {
    transform: translateY(-2px);
  }

  100% {
    transform: translateY(0);
  }
}
</style>
