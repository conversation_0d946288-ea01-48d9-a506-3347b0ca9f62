<script setup lang="ts">
import { PROVINCES } from '@/constants'
import { ref } from 'vue'

const list = Array.from({ length: 15 }).fill('0').map(() => {
  const region = PROVINCES[Math.floor(Math.random() * PROVINCES.length)] // 随机
  const letter = ['A', 'B', 'C', 'D', 'E'][Math.floor(Math.random() * 5)] // 字母 A-E随机
  const number = Math.floor(Math.random() * 10) // 数字 0-9随机
  const name = `${region}${letter}****${number}刚刚获取了`
  const quota = Math.floor(Math.random() * 90 + 10) // 10-100随机整数
  return {
    name,
    quota, // 额度
  }
})

const plateNumber = ref(list)
</script>

<template>
  <div class="top-box flex items-center">
    <div class="content">
      <van-swipe class="swipe" vertical :show-indicators="false" :autoplay="2000">
        <van-swipe-item v-for="(item, index) in plateNumber" :key="index" class="item">
          {{ item.name }} <span class="orange">{{ item.quota }}万额度</span>
        </van-swipe-item>
      </van-swipe>
    </div>
  </div>
</template>

<style lang='less' scoped>
.top-box {
  .label {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
  }

  .content {
    height: 18px;
    margin-left: 5px;

    .swipe {
      height: 100%;

      .item {
        width: 170px;
        height: 100%;
        background: linear-gradient(90deg, rgba(22, 119, 255, 0.3) 0%, rgba(22, 119, 255, 0) 100%);
        border-radius: 9999px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        line-height: 17px;
        padding: 0 5px;
        white-space: nowrap;

        .orange {
          color: #ff8638;
        }
      }
    }
  }
}
</style>
