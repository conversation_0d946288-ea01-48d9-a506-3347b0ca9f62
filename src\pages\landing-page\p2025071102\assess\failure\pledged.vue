<script setup lang="ts">
import AssessStatus2 from './components/AssessStatus2/index.vue'
import AssessSteps2 from './components/AssessSteps2/index.vue'
import PageFooter from '@/components/PageFooter/index.vue'
import PageFooter2 from '@/components/PageFooter/PageFooter2.vue'

import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import { useRoute } from 'vue-router'
import { onMounted, ref } from 'vue'

const route = useRoute()
const plateNumber = ref(route.query.plateNumber as string || '')

const TRACK_PAGE = 'pledgeFailurePage'

const track = useTrack()

// 页面展示 - 埋点
function trackShow() {
  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
      extendJson: {
        pageType: 1,
      },
    })
  })
}

onMounted(() => {
  trackShow()
})
</script>

<template>
  <div class="assess-page">
    <AssessStatus2 :info="{ licensePlateNumber: plateNumber }" />
    <AssessSteps2 />

    <PageFooter
      company-name="杭州优易润科技有限公司" icp="浙ICP备**********号-3" phone="**********"
    />
    <PageFooter2 />
  </div>
</template>

<style lang='less' scoped>
.assess-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f9ff;
  background: linear-gradient(181deg, #1677ff 0%, #1677ff 100px, #f8faff 400px, #f8faff 100%);

  .icon-back {
    width: 9px;
    height: 15px;
    margin-left: 14px;
    margin-right: 8px;
  }

  .nav-title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 17px;
    color: #000000;
    line-height: 24px;
  }

  .footer {
    margin-top: auto;
    margin-bottom: 30px;
  }
}
</style>
