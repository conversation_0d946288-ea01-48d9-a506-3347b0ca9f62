import track from '@/utils/track'
import { getTrackInfo } from '@/api/home'
import { useCountdownApp } from '@/composables/useCountdownApp'

/**
 * 留资节点 (0:未登录 1:手机号 2:车牌信息 3:身份证信息 4:留资完成 5:车辆状态),
private Integer userInfoNode;
 */
enum TrackUserInfoNode {
  UNLOGIN = 0,
  PHONE = 1,
  CAR = 2,
  ID = 3,
  FINISH = 4,
  CAR_STATUS = 5,
}

/**
 * 贷款记录节点 (1:未创建；2:已创建；3:预审；4:电核；5:授信；6:放款)
private Integer loadNode;
 */
enum TrackLoadNode {
  UNLOGIN = 0,
  UNCREATE = 1,
  CREATE = 2,
  PRE = 3,
  ELEC = 4,
  CREDIT = 5,
  LOAN = 6,
}

/**
 * 贷款记录状态 (1:已通过;2:未通过)
private Integer loanStatus;
 */
enum TrackLoanStatus {
  UNLOGIN = 0,
  PASS = 1,
  UNPASS = 2,
}

export interface TrackExtendJson {
  userInfoNode: TrackUserInfoNode
  loadNode: TrackLoadNode
  loanStatus: TrackLoanStatus
  childProduceType?: number
  cityId?: number
}

interface TrackShowParams {
  page: string
  extendJson?: Record<string, unknown>
}

// 点击按钮：1.返回 2.车牌号点击 3.车牌号输入完成 4.所在城市点击 5.车辆状态点击 6.身份证上传点击 7.身份证上传完成 8.一键获取额度 9.输入姓名 10.输入姓名完成 11.借款金额5万，12.借款金额10万，13.借款金额20万，14.借款金额50万，15.借款金额100万
export enum TrackClickButton {
  BACK = 1,
  CAR = 2,
  FINISH = 3,
  CITY = 4,
  CAR_STATUS = 5,
  ID = 6,
  FINISH_ID = 7,
  GET_QUOTA = 8,
  NAME = 9,
  FINISH_NAME = 10,
  LOAN_AMOUNT_5 = 11,
  LOAN_AMOUNT_10 = 12,
  LOAN_AMOUNT_20 = 13,
  LOAN_AMOUNT_50 = 14,
  LOAN_AMOUNT_100 = 15,
}

interface TrackClickParams extends TrackShowParams {
  button: TrackClickButton | number // 支持枚举和自定义数字值
  extendJson?: Record<string, unknown>
}

const InitialExtendJson = {
  userInfoNode: 0,
  loadNode: 0,
  loanStatus: 0,
  childProduceType: null,
  cityId: null,
}

export function useTrack() {
  const info = ref({
    ...InitialExtendJson,
  } as TrackExtendJson)

  onMounted(() => {
    refresh()
  })

  // 刷新埋点信息
  function refresh() {
    return getTrackInfo().then((res: any) => {
      if (res.code === 200 && res.data) {
        info.value = {
          userInfoNode: res.data.userInfoNode || 0,
          loadNode: res.data.loadNode || 0,
          loanStatus: res.data.loanStatus || 0,
          childProduceType: res.data.childProduceType ?? null,
          cityId: res.data.cityId ?? null,
        }
      }
    })
  }

  // 显示埋点
  function show(params: TrackShowParams) {
    try {
      return track.show({
        ...params,
        extendJson: {
          ...info.value,
          ...params.extendJson,
        },
      })
    }
    catch (error) {
      return Promise.reject(error)
    }
  }

  // 点击埋点
  function click(params: TrackClickParams) {
    try {
      return track.click({
        ...params,
        extendJson: {
          ...info.value,
          ...params.extendJson,
        },
      })
    }
    catch (error) {
      return Promise.reject(error)
    }
  }

  /**
   * 停留埋点
   * timerValue: 触发间隔时间
   * track.stay(): 调用默认启动 start() 方法
   * track.stay.stop(): 停止
   * track.stay.pause(): 暂停
   * track.stay.resume(): 恢复
   */
  interface StayControls {
    stop: () => void
    pause: () => void
    resume: () => void
  }

  let stayControls: StayControls | null = null

  function stay(params: TrackShowParams & { timerValue?: number }) {
    try {
      const { timerValue = 20 } = params
      // 如果已经有计时器在运行，先停止它
      stay.stop()

      // 使用倒计时
      const { start, stop, pause, resume } = useCountdownApp({
        initialValue: timerValue,
        onComplete: () => {
          track.stay({
            ...params,
            extendJson: {
              ...info.value,
              ...params.extendJson,
            },
          })
          // 重新开始倒计时，实现循环
          start()
        },
      })

      // 保存控制方法
      stayControls = { stop, pause, resume }

      // 开始倒计时
      start()
    }
    catch (error) {
      console.error('Error in stay tracking:', error)
    }
  }

  // 添加控制方法
  stay.stop = () => {
    stayControls?.stop()
    stayControls = null
  }
  stay.pause = () => stayControls?.pause()
  stay.resume = () => stayControls?.resume()

  return {
    info,
    refresh,
    click,
    show,
    stay,
  }
}
