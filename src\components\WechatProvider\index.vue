<script setup lang="ts">
import { isInWechat, isInWechatMiniProgram } from '@/utils'
import { useTrack } from '@/composables/useTrack'
import { computed, ref, watch } from 'vue'
import { Loading } from 'vant'
import { systemConfig } from '@/api/home'

const guidePageRef = ref<HTMLElement>(null)
const isLoading = ref(true) // 是否正在加载
const isShowGuide = ref(true) // 是否展示微信引导页
const isWechat = computed(() => isInWechat() && !isInWechatMiniProgram()) // 微信环境且不是小程序

const track = useTrack({
  page: 'wechatGuidePage',
  autoShow: false,
  autoStay: false,
})

watch(
  () => guidePageRef.value,
  (newVal, oldVal) => {
    if (newVal && !oldVal) {
      track.refresh().then(() => {
        track.show({
          page: 'wechatGuidePage',
        })
      })
    }
  },
  { immediate: true, deep: true },
)

systemConfig('WEI_XIN_GUIDE')
  .then((res: any) => {
    if (res.code === 200) {
      isShowGuide.value = String(res?.data) === '0' // 0:展示 1：不展示
    }
  })
  .finally(() => {
    isLoading.value = false
  })
</script>

<template>
  <div v-if="isLoading" class="flex justify-center items-center h-100vh w-100vw">
    <Loading />
  </div>
  <div v-else-if="isWechat && isShowGuide" ref="guidePageRef" class="guide-page">
    <img class="icon" src="@/assets/images/common/guide_wechat.png">
  </div>
  <slot v-else />
</template>

<style lang='less' scoped>
.guide-page {
  width: 375px;
  min-height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);

  .icon {
    width: 283px;
    height: 228px;
    display: block;
    margin: 0 40px 0 52px;
  }
}
</style>
