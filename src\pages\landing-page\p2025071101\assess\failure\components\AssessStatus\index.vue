<script setup lang="ts">
</script>

<template>
  <div class="status flex flex-col">
    <img class="icon" src="@/pages/landing-page/p2025032401/images/status_wait.png">
    <div class="title">
      已收到您的申请
    </div>
    <div class="subtitle">
      请耐心等待…
    </div>
  </div>
</template>

<style lang='less' scoped>
.status {
  .icon {
    width: 52px;
    height: 52px;
    margin: 30px auto 0;
  }

  .title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    line-height: 22px;
    margin: 15px auto 0;
    text-align: center;
  }

  .subtitle {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 17px;
    margin: 5px auto 0;
    text-align: center;
  }
}
</style>
