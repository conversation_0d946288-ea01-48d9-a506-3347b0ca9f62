<script setup lang="ts">
import GaugeEcharts from '@/components/GaugeEcharts/index.vue'
import RollingNumber from '@/components/RollingNumber/index.vue'
import IconCheckWhite from '@/components/IconCheckWhite/index.vue'
import checkImg from '@/assets/images/2025070801/checked.png'
// import dialogMount from '@/utils/dialog-mount'
import { useTrack } from '@/composables/useTrack'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { useProtocol } from '@/composables/useProtocol'
// import { useDialog } from '@revfanc/use'

defineProps({
  phone: {
    type: [Number || String],
    default: '',
  },
})

const emit = defineEmits(['action'])

const { isUnionLogin } = useUnionLogin()
const { showProtocol } = useProtocol({ isInstitutionalAgreementVisible: true, isHidePrivacyPolicyAndUserAgreement: !isUnionLogin.value })

// const dialog = useDialog()
const readProtocol = useLocalStorage('read_protocol', false)
// const isShowConsentAuthorizationIntercept = ref(false)
// const DialogConsentAuthorization = dialogMount(() => import('@/components/Dialog/ConsentAuthorization.vue'))
// const ConsentAuthorizationIntercept = dialogMount(() => import('@/components/Dialog/ConsentAuthorizationIntercept.vue'))

// 1:关闭，2：授权相关协议，3：同意并继续
enum TrackClickButton {
  CLOSE = 1,
  PROTOCOL = 2,
  CONFIRM = 3,
}

const track = useTrack({
  page: 'certificationPop',
  autoShow: true,
  autoStay: true,
})

function trackClick(button: TrackClickButton) {
  track.click({ button })
}

function handleSubmit() {
  readProtocol.value = true
  emit('action', { action: 'confirm' })
  trackClick(TrackClickButton.CONFIRM)
}

// 同意授权对话框拦截
// function handleConsentAuthorizationIntercept() {
//   if (isShowConsentAuthorizationIntercept.value) {
//     return Promise.reject(new Error('已拒绝授权'))
//   }

//   isShowConsentAuthorizationIntercept.value = true

//   return dialog.open({
//     render(context) {
//       return h(ConsentAuthorizationIntercept, {
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action !== 'confirm') {
//       return Promise.reject(res)
//     }

//     // 同意协议
//     readProtocol.value = true

//     return Promise.resolve(res)
//   })
// }

// 同意授权对话框
// function handleConsentAuthorization() {
//   return dialog.open({
//     render(context) {
//       return h(DialogConsentAuthorization, {
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action !== 'confirm') {
//       return Promise.reject(res)
//     }

//     // 同意协议
//     readProtocol.value = true

//     return Promise.resolve(res)
//   }).catch((e) => {
//     if (e.action !== 'confirm') {
//       return handleConsentAuthorizationIntercept()
//     }
//     return Promise.reject(e)
//   })
// }
</script>

<template>
  <div class="dialog-form">
    <img
      class="close" src="@/assets/images/home/<USER>"
      @click="$emit('action', { action: 'close' }), trackClick(TrackClickButton.CLOSE)"
    >
    <div class="title">
      <img :src="checkImg" alt="checkImg">
      <span class="title-text">恭喜你获得 <span class="highlight">车主专属</span> 额度</span>
    </div>
    <div class="progress-container">
      <GaugeEcharts :tick-inner-distance="20" :tick-outer-distance="16" :circle-r="122" :endpoint-border-width="1" :duration="1300" />
      <div class="rolling-num">
        <div class="gray-text w-100%">
          贷款额度最高
        </div>
        <div class="rolling-num-blue flex w-100% items-center justify-center">
          <RollingNumber :start="55" :target="100" :duration="1300" :increment="1" :delay="0" />万
        </div>
      </div>
    </div>
    <div class="img-container">
      <div class="img-item">
        <img src="@/assets/images/p2025072201/icon-1.png" alt="1">
        <span class="text">最长48期</span>
      </div>
      <div class="img-item">
        <img src="@/assets/images/p2025072201/icon-2.png" alt="2">
        <span class="text">不押车</span>
      </div>
      <div class="img-item">
        <img src="@/assets/images/p2025072201/icon-3.png" alt="3">
        <span>快至1小时放款</span>
      </div>
    </div>
    <div>
      <div class="protocol" @click="readProtocol = !readProtocol">
        <IconCheckWhite class="icon" :checked="readProtocol" />
        <span class="text">我已阅读并同意<span
          class="blue"
          @click.stop="showProtocol(isUnionLogin ? 'privacy-policy' : 'user-share')"
        >《授权相关协议》
        </span>
        </span>
      </div>

      <button class="submit-btn bounce" @click="handleSubmit">
        <img class="hand" src="@/assets/images/home/<USER>">
      </button>
    </div>
  </div>
</template>

<style lang='less' scoped>
.dialog-form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 15px;
  width: 305px;
  height: 380px;
  position: relative;
  border-radius: 16px;
  background: linear-gradient(180deg, #d0deff 0%, #ffffff 30%);
  box-shadow: 0px 0px 3px 0px rgba(0, 49, 118, 0.3);
  .close {
    width: 21px;
    height: 21px;
    position: absolute;
    right: 10px;
    top: -40px;
    z-index: 999;
  }
  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-top: 20px;
    img {
      width: 25px;
      height: 25px;
    }
    .title-text {
      font-family: MiSans, MiSans;
      font-weight: bold;
      font-size: 20px;
      color: #333333;
      line-height: 27px;
      text-align: left;
      font-style: normal;

      .highlight {
        color: #ff7711;
      }
    }
  }
  .progress-container {
    margin: -4px auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .rolling-num {
      position: absolute;
      width: 100%;
      top: 70px;
      text-align: center;
    }
    .gray-text {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 10px;
      color: #333333;
      line-height: 14px;
    }
    .rolling-num-blue {
      font-family: MiSans, MiSans;
      font-weight: 800;
      font-size: 48px;
      color: #1677ff;
      line-height: 64px;
    }
  }
  .img-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
    padding: 0 38px;
    .img-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 5px;
      &:nth-child(2) {
        margin-left: 16px;
      }
      &:nth-child(3) {
        margin-right: -12px;
      }
      img {
        width: 36px;
        height: 36px;
      }
      span {
        font-weight: 500;
        font-size: 12px;
        color: #333333;
        line-height: 17px;
        text-align: center;
        font-style: normal;
      }
    }
  }

  .protocol {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    text-align: center;
    margin: 10px auto 0;

    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-right: 5px;
      margin-top: 2px;

      &.active {
        color: #1677ff;
      }
    }

    .text {
      vertical-align: middle;

      .blue {
        color: #1677ff;
      }
    }
  }

  .submit-btn {
    display: block;
    width: 265px;
    height: 57px;
    margin: 10px auto;
    position: relative;
    background-image: url('@/assets/images/p2025072201/btn-1.png');
    background-size: 100% 100%;

    .hand {
      width: 50px;
      height: 60px;
      position: absolute;
      right: -11px;
      bottom: -30px;
    }
  }
}
</style>
