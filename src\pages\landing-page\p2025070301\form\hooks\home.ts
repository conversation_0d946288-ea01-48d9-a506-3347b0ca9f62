import { onMounted, ref } from 'vue'
import { chooseImage, compressImage, formatPrice } from '@/utils'
import {
  carLoansApply,
  carLoansApplyV2,
  carLoansInfo,
  setUserExtend,
  tencentIdCardOcrV2,
  uploadImage,
} from '@/api/home'
import type { UserExtend } from '@/api/home'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { useLocalStorage } from '@vueuse/core'
import dayjs from 'dayjs'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { useProtocol } from '@/composables/useProtocol'

const STORAGE_READ_PROTOCOL_KEY = 'read_protocol'

export function useHome(isClue: boolean = false) {
  const router = useRouter()
  const route = useRoute()
  const { loginType } = useUnionLogin()

  const { showProtocol, showConsentAuthorization2 } = useProtocol()

  const shouldUpdateFormData = ref(true) // 是否可以更新表单数据

  const agreementCheckTime = ref() // 协议勾选时间
  const agreementDialogConfirmTime = ref() // 协议弹窗确认时间

  const readProtocol = useLocalStorage(STORAGE_READ_PROTOCOL_KEY, false)

  const plateNumberRef = ref(null)

  const plateNumber = ref('')

  const region = ref({
    cityId: null,
    cityName: null,
    provinceId: null,
    provinceName: null,
  })

  const carStatus = ref(1)

  const identityCards = ref({
    front: '',
    back: '',
  }) // 身份证图片

  const InitFormData = {
    idCardFrontUrl: null,
    idCardBackUrl: null,
    idCardNumber: null,
    realName: null,
    validDate: null,
    race: null,
    idAddress: null,
    issuedBy: null,
  }

  const formData = ref({
    ...InitFormData,
  } as UserExtend)

  const info = ref({
    status: null,
    outOrderNo: '',
    creditLine: null,
    idCardFrontUrl: '',
    idCardBackUrl: '',
    licensePlateNumber: '',
    signUrl: '',
    cityId: null,
    cityName: null,
    provinceId: null,
    provinceName: null,
    annualInterestRate: 6,
    dailyInterest: null,
  } as any) // 接口数据

  async function getInfo(): Promise<void> {
    const res: any = await carLoansInfo()
    if (res.code === 200 && res.data) {
      info.value = { ...res.data }

      if (shouldUpdateFormData.value) {
        if (info.value.licensePlateNumber) {
          plateNumber.value = info.value.licensePlateNumber
        }

        if (info.value.cityId) {
          region.value = {
            cityId: info.value.cityId,
            cityName: info.value.cityName,
            provinceId: info.value.provinceId,
            provinceName: info.value.provinceName,
          }
        }

        if (info.value.vehicleStatus) {
          carStatus.value = info.value.vehicleStatus
        }

        if (info.value.idCardFrontUrl) {
          identityCards.value.front = info.value.idCardFrontUrl
        }

        if (info.value.idCardBackUrl) {
          identityCards.value.back = info.value.idCardBackUrl
        }
      }
    }
  }

  /**
   * 上传图片
   * @param {string} type 正反面 'front' | 'back'
   */
  function goPicture(type: string) {
    shouldUpdateFormData.value = false

    return chooseImage()
      .then((file) => {
        // 小于 1M 的图片不进行压缩
        if (file.size < 1024 * 1024) {
          return Promise.resolve(file)
        }

        return compressImage(file)
      })
      .then((file) => {
        showLoadingToast({
          message: '识别中...',
          duration: 15000,
          forbidClick: true,
        })
        return uploadImage(file)
          .then((res: any) => {
            if (res.code !== 200) {
              throw res
            }
            return res.data
          })
      })
      .then((url) => {
        return tencentIdCardOcrV2({
          idCardUrl: url,
          cardSide: type === 'front' ? 1 : 2,
        }).then((res: any) => {
          if (res.code !== 200) {
            throw res
          }
          return { ...res.data, url }
        })
      })
      .then((res: any) => {
        const { url, ...rest } = res
        identityCards.value[type] = url
        console.warn('rest :>> ', rest)
        const params: UserExtend = {
          idCardNumber: rest.idCard,
          realName: rest.name,
          validDate: rest.validDate,
          race: rest.nation,
          idAddress: rest.address,
          issuedBy: rest.authority,
        }

        Object.keys(params).forEach((key) => {
          if (!params[key]) {
            delete params[key]
          }
        })

        formData.value = {
          ...formData.value,
          ...params,
        }

        showToast('识别成功')
      })
      .finally(() => {
        shouldUpdateFormData.value = true
      })
  }

  // 上传图片引导
  function goUploadTips() {
    // native.showCardTipsDialog(trackExtendJson)
  }

  // 更新车牌&地区
  async function updatePlateNumber() {
    if (!plateNumber.value || plateNumber.value.length < 7) {
      showToast('请输入车牌号评估')

      plateNumberRef.value.shake()

      return Promise.reject(new Error('请输入车牌号评估'))
    }
    const params = {
      licensePlateNumber: plateNumber.value,
      provinceId: region.value.provinceId,
      provinceName: region.value.provinceName,
      cityId: region.value.cityId,
      cityName: region.value.cityName,
      vehicleStatus: carStatus.value,
    }

    return updateUser(params)
  }

  // 更新身份证信息
  async function updateIdentityCard() {
    if (!isClue && (!identityCards.value.front || !identityCards.value.back)) {
      showToast('请上传身份证照片')
      return Promise.reject(new Error('请上传身份证照片'))
    }
    const params = {
      idCardFrontUrl: identityCards.value.front,
      idCardBackUrl: identityCards.value.back,
    }

    // 添加身份证识别信息
    Object.keys(formData.value).forEach((key) => {
      if (formData.value[key]) {
        params[key] = formData.value[key]
      }
    })

    return updateUser(params)
  }

  async function updateAllInfo() {
    const params = {
      reset: info.value?.status >= 1,
      licensePlateNumber: plateNumber.value,
      provinceId: region.value.provinceId,
      provinceName: region.value.provinceName,
      cityId: region.value.cityId,
      cityName: region.value.cityName,
      vehicleStatus: carStatus.value,
      idCardFrontUrl: identityCards.value.front,
      idCardBackUrl: identityCards.value.back,
    }

    // 添加身份证识别信息
    Object.keys(formData.value).forEach((key) => {
      if (formData.value[key]) {
        params[key] = formData.value[key]
      }
    })

    showLoadingToast({
      message: '正在更新信息',
      duration: 15 * 1000,
      forbidClick: true,
    })

    console.warn('params :>> ', params)

    return updateUser(params).finally(() => {
      formData.value = {
        ...InitFormData,
      }
      closeToast()
    })
  }

  // 更新用户数据
  function updateUser(params: UserExtend) {
    if (info.value.status >= 2) {
      goResult(info.value.outOrderNo)
      return Promise.reject(new Error('已申请过贷款'))
    }

    return setUserExtend(params).then((res: any) => {
      if (res.code === 200) {
        getInfo()
        return res
      }

      if (String(res.code) === '800001') {
        closeToast()
        goResult(info.value.outOrderNo, res.msg)
        return res
      }

      if (res?.msg && String(res.code) !== '800001') {
        showToast(res.msg)
      }

      return Promise.reject(new Error(res))
    })
  }

  // 获取存证参数
  function getSaveEvidenceParams(code: number) {
    const CODE = {
      [-1]: -1, // 进件页面
      33: 33, // 进件页面
      34: 34, // 授权共享信息确认弹窗
    }
    return {
      agreementPageCode: CODE[code],
      confirmDateTime: agreementDialogConfirmTime.value ? dayjs(agreementDialogConfirmTime.value).format('YYYY-MM-DD HH:mm:ss') : '',
      clickCheckBoxTime: agreementCheckTime.value ? dayjs(agreementCheckTime.value).format('YYYY-MM-DD HH:mm:ss') : '',
    }
  }

  // 申请贷款
  async function applyLoan(agreementPageCode?: number): Promise<any> {
    try {
      if (String(loginType.value) === '1' || String(loginType.value) === '2') {
        agreementPageCode = 33
      }
      else {
        agreementPageCode = -1
      }

      if (!plateNumber.value || plateNumber.value.length < 7) {
        showToast('请输入车牌号评估')

        plateNumberRef.value.shake()

        return Promise.reject(new Error('请输入车牌号评估'))
      }

      if (!isClue && (!identityCards.value.front || !identityCards.value.back)) {
        showToast('请上传身份证照片')
        return Promise.reject(new Error('请上传身份证照片'))
      }

      if (!readProtocol.value && (String(loginType.value) === '1' || String(loginType.value) === '2')) {
        const dialogRes = await showConsentAuthorization2()

        if (dialogRes.action !== 'confirm') {
          return Promise.reject(new Error('用户点击拒绝授权弹窗'))
        }
        readProtocol.value = true
        agreementPageCode = 34
        agreementCheckTime.value = ''
      }

      agreementDialogConfirmTime.value = Date.now()

      // 更新数据
      await updateAllInfo()

      const params = getSaveEvidenceParams(agreementPageCode)

      const api = isClue ? carLoansApplyV2 : carLoansApply

      // 申请进件
      const res: any = await api(params)

      if (res.code === 200 && res.data) {
        showToast('申请成功')

        getInfo()

        goResult(res.data)

        return res
      }

      if (String(res.code) === '800001') {
        // closeToast()
        goResult(info.value.outOrderNo, res.msg)
        return res
      }

      if (res?.msg && String(res.code) !== '800001') {
        showToast(res.msg)
      }

      return Promise.reject(res)
    }
    catch (error) {
      console.warn('流程终止：', error)
    }
  }

  function goResult(orderNo: string = info.value.outOrderNo, errMsg?: string) {
    if (!orderNo) {
      return
    }

    // if (isAndroid) {
    //   router.push({
    //     path: `${route.path.replace('form', 'download')}`,
    //   })
    //   return
    // }

    router.push({
      path: `${route.path.replace('form', 'assess')}`,
      query: {
        ...route.query,
        orderNo,
        errMsg,
      },
    })
  }

  onMounted(() => {
    getInfo().finally(() => {
      // 大联登自动进件
      if (String(loginType.value) === '2') {
        readProtocol.value = true
        applyLoan()
      }
    })
  })

  return {
    info,
    region,
    identityCards,
    getInfo,
    plateNumberRef,
    plateNumber,
    carStatus,
    readProtocol,
    agreementCheckTime,
    agreementDialogConfirmTime,
    showProtocol,
    loginType,
    goPicture,
    goUploadTips,
    goResult,
    formatPrice,
    updatePlateNumber,
    updateIdentityCard,
    updateUser,
    updateAllInfo,
    applyLoan,
  }
}
