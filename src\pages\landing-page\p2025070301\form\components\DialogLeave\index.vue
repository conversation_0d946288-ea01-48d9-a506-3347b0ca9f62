<script setup lang="ts">
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'

const emit = defineEmits(['action'])
const track = useTrack()

// 埋点按钮值定义 1关闭 2继续借款 3仍然退出
const TRACK_BUTTONS = {
  CLOSE: 1, // 关闭按钮
  CONTINUE: 2, // 继续借款
  LEAVE: 3, // 仍然退出
} as const

// 埋点页面名称
const TRACK_PAGE = 'addInfoPageReturn1'

function handleView() {
  // 点击立即查看按钮埋点
  track.click({ page: TRACK_PAGE, button: TRACK_BUTTONS.CONTINUE })

  emit('action', { action: 'confirm' })
}

function handleClose() {
  emit('action', { action: 'close' })
}

// 监听弹窗显示
track.show({ page: TRACK_PAGE })
</script>

<template>
  <div class="dialog-content">
    <img
      class="close" src="@/pages/landing-page/p2025040901/images/close_w.png"
      @click="handleClose(), track.click({ page: TRACK_PAGE, button: TRACK_BUTTONS.CLOSE })"
    >
    <div class="bar">
      <div class="line">
        <div class="circle" />
      </div>
    </div>
    <div class="content">
      <img
        class="cancel" src="@/pages/landing-page/p2025040901/images/popup2_btn1.png"
        @click="handleClose(), track.click({ page: TRACK_PAGE, button: TRACK_BUTTONS.LEAVE })"
      >
      <img class="confirm" src="@/pages/landing-page/p2025040901/images/popup2_btn2.png" @click="handleView">
    </div>
  </div>
</template>

<style lang="less" scoped>
.dialog-content {
  width: 307px;
  height: 285px;
  background: url('@/pages/landing-page/p2025040901/images/popup2_bg.png') no-repeat center;
  background-size: contain;
  display: flex;
  flex-direction: column;

  .close {
    position: absolute;
    top: -41px;
    right: 0;
    width: 21px;
    height: 21px;
    cursor: pointer;
    z-index: 1;
  }

  .bar {
    width: 182px;
    height: 14px;
    border-radius: 9999px;
    position: absolute;
    left: 36px;
    top: 167px;
    overflow: hidden;

    .line {
      width: 182px;
      height: 14px;
      background: #1677ff;
      border-radius: 9999px;
      position: absolute;
      left: -100%;
      top: 0;
      animation: right 2s 0.5s linear both;

      .circle {
        width: 10px;
        height: 10px;
        background: #ffffff;
        border-radius: 50%;
        position: absolute;
        right: 2.5px;
        top: 2.5px;

        &::after {
          content: '当前进度';
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 10px;
          color: rgba(255, 255, 255, 0.6);
          line-height: 14px;
          position: absolute;
          left: -44px;
          top: 50%;
          transform: translateY(-50%);
          white-space: nowrap;
        }
      }
    }
  }

  @keyframes right {
    to {
      transform: translateX(180px);
    }
  }

  .content {
    margin-top: 208px;
    display: flex;
    justify-content: center;

    .cancel {
      width: 104px;
      height: 49px;
      margin-top: 3px;
    }

    .confirm {
      width: 168px;
      height: 59px;
      margin-left: 10px;
    }
  }
}
</style>
