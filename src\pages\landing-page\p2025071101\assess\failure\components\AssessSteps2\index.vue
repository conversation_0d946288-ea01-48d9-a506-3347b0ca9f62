<script setup lang="ts">
</script>

<template>
  <div class="steps flex flex-col">
    <div class="title flex items-center">
      <img class="logo" src="@/pages/landing-page/p2025032401/images/logo.png">
      <span>好信车贷正在为你办理</span>
    </div>
    <!-- 申请失败 -->

    <div class="step">
      <div class="item">
        <img class="status" src="@/pages/landing-page/p2025032401/images/step_warning.png">
        <div class="text red">
          车辆评估未通过
          <div class="explain">
            未通过原因：已抵押绿本暂不支持
          </div>
        </div>
      </div>
      <div class="item">
        <img class="status" src="@/pages/landing-page/p2025032401/images/step_wait.png">
        <div class="text">
          额度评估
        </div>
      </div>
      <div class="item">
        <img class="status" src="@/pages/landing-page/p2025032401/images/step_wait.png">
        <div class="text">
          返回额度
        </div>
      </div>
    </div>
  </div>
</template>

<style lang='less' scoped>
.steps {
  width: 351px;
  background: #ffffff;
  box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.05);
  border-radius: 8px;
  margin: 18px auto 0;

  .title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    margin: 20px 7px 0;

    .logo {
      width: 22px;
      height: 22px;
      margin-right: 1px;
    }
  }

  .step {
    margin: 20px 30px 0;

    .item {
      display: flex;

      &:not(:last-child) {
        min-height: 42px;
        position: relative;

        &::after {
          content: '';
          width: 1px;
          height: calc(100% - 16px);
          position: absolute;
          left: 8px;
          top: 20px;
          background: linear-gradient(to bottom, transparent 0%, transparent 50%, #438bf8 50%, #438bf8 100%);
          background-size: 1px 6px;
          background-repeat: repeat-y;
        }
      }

      &:last-child {
        margin-bottom: 20px;
      }

      .status {
        width: 16px;
        height: 16px;
        margin-right: 5px;
        margin-top: 4px;
        object-fit: contain;

        &.rotate {
          animation: rotate 1s linear infinite;
        }
      }

      .text {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;

        &.red {
          color: #ff6c6c;
          font-weight: bold;
        }

        &.blue {
          color: #438bf8;
          font-weight: bold;
        }

        &.grey {
          color: #999999;
        }

        .explain {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 17px;
          margin-top: 5px;
          margin-bottom: 15px;
        }

        .call {
          height: 32px;
          background: rgba(47, 121, 255, 0.1);
          border-radius: 17px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          margin-top: 5px;
          margin-bottom: 15px;
          padding: 0 8px 0 3px;

          .avatar {
            width: 26px;
            height: 26px;
            margin: 0 5px 0 0;
          }
        }
      }
    }
  }

  .limit {
    width: 311px;
    height: 147px;
    background: #f0f6ff;
    border-radius: 8px;
    margin: 20px auto 0;
    text-align: center;

    .text1 {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      margin: 10px auto 0;
    }

    .text2 {
      min-width: 223px;
      height: 65px;
      background-image: url('@/pages/landing-page/p2025032401/images/bg_amount.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin: 15px auto 0;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 42px;
      color: #333333;
      line-height: 59px;
    }

    .text3 {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      line-height: 17px;
      margin: 10px auto 0;
    }
  }

  .hot {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 12px;
    color: #333333;
    line-height: 16px;
    margin: 15px auto 0;

    .icon {
      width: 16px;
      height: 16px;
      margin: 0 6px 0 0;
    }
  }

  .button {
    width: 275px;
    height: 49px;
    background: #1677ff;
    box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);
    border-radius: 9999px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 18px;
    color: #ffffff;
    line-height: 25px;
    display: block;
    margin: 15px auto 0;
  }

  .desc {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #1677ff;
    line-height: 17px;
    margin: 15px auto;
    text-align: center;
    text-decoration: underline;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
