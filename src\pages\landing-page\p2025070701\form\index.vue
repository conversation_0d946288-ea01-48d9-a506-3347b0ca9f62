<script setup lang="ts">
import NavBar from '@/components/NavBar/index.vue'
import PlateNumber from './components/PlateNumber/index.vue'
import RegionPicker from '@/components/RegionPicker/index.vue'
import CarStatus from '@/components/CarStatus/index.vue'
import PlateNumberSwipe from '@/components/PlateNumberSwipe/index.vue'
// import IdentityCard from './components/IdentityCard/index.vue'
import PageFooter from '@/components/PageFooter/index.vue'
import IconCheckWhite from '@/components/IconCheckWhite/index.vue'

import dialogMount from '@/utils/dialog-mount'
import { setUserExtend } from '@/api/home'
// import { usePlateNumberCardDialog } from '@/composables/usePlateNumberCardDialog'

import { useForm } from '@/pages/composables/useForm'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import { TrackClickButton } from '@/enum/track'
import { ref } from 'vue'
import { useNoBehavior } from '@/composables/useNoBehavior'
import { usePageBackInterceptor } from '@/composables/usePageBackInterceptor'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { isLogin } from '@/utils/auth'
import { useDialog } from '@revfanc/use'

defineOptions({
  beforeRouteEnter(to, from, next) {
    if (isLogin()) {
      next()
    }
    else {
      next({
        path: to.path.replace('/form', ''),
        query: {
          ...from.query,
        },
      })
    }
  },
})

const dialog = useDialog()

const noBehavior = useNoBehavior()

dialog.interceptors.before.use((opts) => {
  noBehavior.pause()
  return opts
})

dialog.interceptors.after.use((opts) => {
  noBehavior.resume()
  return opts
})

// const DialogLoginUpdate = defineAsyncComponent(() => import('@/pages/landing-page/p2025040901/components/DialogLoginUpdate/index.vue'))
// const DialogForm = defineAsyncComponent(() => import('./components/DialogForm/index.vue'))
const DialogAmountIntercept = dialogMount(() => import('@/pages/landing-page/components/Dialog/PageInterceptIdentityV2/index.vue'))

const isBack = ref(false)

const keyboardFocus = ref(false)

const pageBackInterceptor = usePageBackInterceptor()
const unionLogin = useUnionLogin()

const {
  info,
  region,
  realNameRef,
  realName,
  // identityCards,
  plateNumberRef,
  plateNumber,
  carStatus,
  readProtocol,
  agreementCheckTime,
  isUnionLogin,
  showProtocol,
  getInfo,
  // goPicture,
  applyLoan,
} = useForm({
  isClue: true,
  isCheckIdentityCardByDialog: true,
  isInstitutionalAgreementVisible: true,
  footer: {
    record: 'CQ',
  },
})

// const showIdentityCard = computed(() => plateNumber.value?.length >= 7)

const TRACK_PAGE = 'userInfoPage'

const track = useTrack()

// const plateNumberCardDialog = usePlateNumberCardDialog()

function updateUserExtend(params: any) {
  console.log('updateUserExtend-params', params)
  return setUserExtend(params).then((res: any) => {
    if (res.code === 200) {
      return res
    }

    if (res?.msg)
      showToast(res.msg)

    return Promise.reject(res)
  })
}

// 存储登录时选择的归属地
const loginRegion = useSessionStorage('CARS_REGION', {
  cityId: '',
  cityName: '',
  provinceId: '',
  provinceName: '',
})
updateUserExtend(loginRegion.value).then(() => {
  getInfo()
})

watch(() => keyboardFocus.value, (val) => {
  if (val) {
    scrollPageToMiddle('plateNumber')
  }
  else if (!val && plateNumber.value?.length >= 7) {
    scrollPageToMiddle('identityCard')
  }
})

function trackClick(button) {
  track.click({
    page: TRACK_PAGE,
    button,
  })
}

function handleBack() {
  trackClick(TrackClickButton.BACK)

  if (!info.value?.licensePlateNumber && plateNumber.value?.length < 7 && !isBack.value) {
    showDialogLeave()
    isBack.value = true
    return false
  }
  // if ((!info.value?.idCardFrontUrl || !info.value?.idCardBackUrl) && (!identityCards.value?.front || !identityCards.value?.back) && !isBack.value) {
  //   showDialogForm('identityCard')
  //   isBack.value = true
  //   return false
  // }

  return true
}

function showDialogLeave() {
  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogAmountIntercept, {
        onAction: context.callback,
      })
    },
  }).then((res) => {
    if (res.action === 'confirm') {
      getInfo()
    }
  })
}

// function showDialogForm(itemType) {
//   return dialog.open({
//     render(context) {
//       return h(DialogForm, {
//         itemType,
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'confirm') {
//       getInfo()
//     }
//   })
// }

// function uploadTwice() {
//   if (identityCards.value.front && identityCards.value.back) {
//     trackClick(TrackClickButton.FINISH_ID)
//   }
// }

async function scrollPageToMiddle(type: string) {
  const target: HTMLElement = document.querySelector('#form')
  if (!target)
    return
  const top = (target.offsetTop + 100) as number
  window.scrollTo({
    top: type === 'plateNumber' ? top - 50 : top,
    behavior: 'smooth',
  })
}

// function invokeKeyboardFocus() {
//   setTimeout(() => {
//     if (plateNumber.value.length < 7) {
//       plateNumberRef.value?.handleShowKeyboard()
//     }
//   }, 500)
// }

function navBackIconClick() {
  const shouldBack = handleBack()

  if (shouldBack) {
    window.history.back()
  }
}

// function showDialogLoginUpdate() {
//   return dialog.open({
//     render(context) {
//       return h(DialogLoginUpdate, {
//         onAction: context.callback,
//       })
//     },
//   }).then((res) => {
//     if (res.action === 'success') {
//       window.location.reload()
//     }
//     return Promise.reject(res)
//   })
// }

function toggleProtocol() {
  readProtocol.value = !readProtocol.value
  if (readProtocol.value) {
    agreementCheckTime.value = Date.now()
    trackClick(TrackClickButton.PROTOCOL)
  }
}

function onNext() {
  realNameRef.value?.blur()

  setTimeout(() => {
    plateNumberRef.value?.handleShowKeyboard()
  }, 400)
}

function handleSubmit() {
  applyLoan()
  trackClick(TrackClickButton.GET_QUOTA)
}

function scrollInputIntoView() {
  const input = realNameRef.value
  if (input) {
    setTimeout(() => {
      input.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }, 300)
  }
}

onMounted(() => {
  // 用户无操作时,自动弹出登录弹窗
  noBehavior.add({
    duration: 500,
    callback: () => {
      if (plateNumber.value.length >= 7) {
        return
      }

      try {
        if (keyboardFocus.value)
          plateNumberRef.value?.handleKeyboardConfirm()
      }
      catch {
      }

      // plateNumberCardDialog.open({ plateNumber }).then((res) => {
      //   if (res.action === 'confirm') {
      //     applyLoan()
      //   }
      // })
    },
  })

  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
    })
  })

  track.stay({
    page: TRACK_PAGE,
  })

  // 属于联登录需要设置返回到指定页面
  if (unionLogin.isUnionLogin.value) {
    pageBackInterceptor.setBackHandler()
  }

  pageBackInterceptor.add(handleBack)

  // invokeKeyboardFocus()
})

onBeforeUnmount(() => {
  pageBackInterceptor.remove()
})
</script>

<template>
  <div class="home-page">
    <NavBar :background-custom-style="{ backgroundColor: '#fff' }">
      <template #left>
        <img class="icon-back" src="@/pages/landing-page/p2025032401/images/icon_back.png" @click="navBackIconClick()">
      </template>
    </NavBar>

    <div class="header">
      <img src="@/assets/images/p2025070701/icon-4.png" alt="icon-4">
      <div class="desc">
        <span class="text-1">恭喜您，已锁定额度</span>
        <span class="text-2">请补充信息领取额度</span>
      </div>
    </div>

    <form id="form" class="form" @submit.prevent>
      <!-- <div class="box-content">

      </div> -->

      <div class="box-content">
        <div class="title">
          真实姓名
        </div>
        <!-- 姓名输入 限制只能输入汉字和字母 11位 -->
        <div class="row">
          <input
            ref="realNameRef"
            v-model.trim="realName"
            class="label input"
            type="text"
            placeholder="请输入您的姓名"
            maxlength="11"
            enterkeyhint="next"
            @input="($event: any) => realName = $event.target.value.replace(/[^a-zA-Z\u4e00-\u9fa5]/g, '')"
            @focus="() => { trackClick(TrackClickButton.NAME); scrollInputIntoView(); }"
            @blur="realName && trackClick(TrackClickButton.FINISH_NAME)"
            @keyup.enter="onNext"
          >
        </div>
      </div>

      <div class="box-content">
        <div class="title">
          车辆状态
        </div>
        <div class="row">
          <CarStatus v-model="carStatus" status1-text="全款" status2-text="车贷·已结清" status3-text="车贷·未结清" @change="trackClick(TrackClickButton.CAR_STATUS)" />
        </div>
      </div>

      <div class="box-content">
        <div class="title flex items-center">
          车辆信息
          <PlateNumberSwipe class="ml-6" />
        </div>
        <div class="row">
          <PlateNumber
            id="plate-number" ref="plateNumberRef" v-model="plateNumber" class="plate"
            @invoke="trackClick(TrackClickButton.CAR)" @done="trackClick(TrackClickButton.FINISH)"
            @focus="keyboardFocus = true" @blur="keyboardFocus = false"
          />
        </div>
        <div class="line" />

        <div class="region-box">
          <div class="region-title">
            归属地
          </div>
          <RegionPicker v-model="region" class="region-picker" @invoke="trackClick(TrackClickButton.CITY)">
            <template #default="{ handleSelect }">
              <div class="row" @click="handleSelect">
                <div class="label">
                  {{ region.cityName || '*' }}
                </div>
                <div class="arrow" />
              </div>
            </template>
          </RegionPicker>
        </div>
      </div>

      <!-- 上传身份证 -->
      <!-- <IdentityCard
        v-show="showIdentityCard"
        class="identity-card" :info="info" :identity-cards="identityCards"
        @picture="goPicture($event).finally(() => { uploadTwice() }), trackClick(TrackClickButton.ID)"
      /> -->
      <div class="footer">
        <button class="submit-btn bounce" @click="handleSubmit()">
          <img class="hand" src="@/assets/images/home/<USER>">
        </button>

        <!-- 协议 -->
        <div class="protocol" @click="toggleProtocol">
          <IconCheckWhite class="icon" :checked="readProtocol" />
          <span class="text">我已阅读并同意<span
            class=""
            @click.stop="showProtocol(isUnionLogin ? 'privacy-policy' : 'user-share')"
          >《授权相关协议》
          </span>
          </span>
        </div>
      </div>
    </form>

    <!-- 页脚 -->
    <PageFooter />

    <div class="footer-placeholder" />
  </div>
</template>

<style scoped lang="less">
.home-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(180deg, #eef4ff 0%, #ffffff 40%);

  .icon-back {
    width: 9px;
    height: 15px;
    margin-left: 14px;
  }

  .header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 30px 0 10px 32px;

    img {
      width: 56px;
      height: 56px;
    }
    .desc {
      display: flex;
      flex-direction: column;
      color: #1677ff;
      .text-1 {
        font-weight: 600;
        font-size: 24px;
      }
      .text-2 {
        font-size: 14px;
      }
    }
  }

  .box-content {
    width: 345px;
    background: #ffffff;
    box-shadow: 0px 0px 3px 0px rgba(0, 49, 118, 0.3);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    margin: 20px auto 0;

    .title {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      margin: 20px 15px 0;
      position: relative;

      &::before {
        content: '';
        width: 2px;
        height: 12px;
        background: #1677ff;
        border-radius: 1px;
        position: absolute;
        left: -5px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .row {
      margin: 20px 10px 0;
      display: flex;
      align-items: center;

      &:last-child {
        margin-bottom: 20px;
      }

      .label {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
      }

      .edit {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #1677ff;
        line-height: 20px;
        margin-left: auto;
      }

      .arrow {
        width: 5px;
        height: 10px;
        margin-left: auto;
        background-image: url('@/assets/images/common/right_arrow.png');
        background-size: 100% 100%;
        transform: rotate(90deg);
      }

      .plate {
        margin: 0 5px;
      }

      .input {
        width: 100%;
        background: #fff;
      }
    }

    .line {
      width: 90%;
      height: 1px;
      background: #eee;
      margin: 20px auto;
    }
  }

  .region-box {
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 10px 20px 16px;

    .region-title {
      color: #333;
      font-weight: 600;
      font-size: 16px;
    }

    .region-picker {
      .label {
        color: #666;
      }
      .arrow {
        margin-left: 8px;
      }
    }
  }

  .form {
    display: flex;
    flex-direction: column;

    :deep(.car-status) {
      .status {
        height: 28px;
        border-radius: 4px;
      }
    }

    .identity-card {
      margin: -14px auto 0;
    }

    .footer {
      width: 375px;
      background: #ffffff;
      box-shadow: 0px -1px 5px 0px rgba(47, 121, 255, 0.1);
      position: fixed;
      left: 0;
      bottom: 0;
      z-index: 19;
      padding-bottom: env(safe-area-inset-bottom);
      padding-bottom: constant(safe-area-inset-bottom);

      .submit-btn {
        display: block;
        width: 331px;
        height: 61px;
        margin: 10px auto;
        position: relative;
        background-image: url('@/pages/landing-page/p2025040901/images/btn2.png');
        background-size: 100% 100%;

        .hand {
          width: 50px;
          height: 60px;
          position: absolute;
          right: -11px;
          bottom: -30px;
        }
      }
    }
  }

  .footer-placeholder {
    height: calc(81px + env(safe-area-inset-bottom));
    height: calc(81px + constant(safe-area-inset-bottom));
  }

  .back-top {
    width: 87px;
    height: 35px;
    margin: 7px auto 0;
    background-image: url('@/assets/images/home/<USER>');
    background-size: 100% 100%;
  }

  .protocol {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 17px;
    text-align: center;
    margin: -5px auto 10px;

    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-right: 5px;
      margin-top: 2px;

      &.active {
        color: #1677ff;
      }
    }

    .text {
      vertical-align: middle;

      .blue {
        color: #1677ff;
      }
    }
  }
}
</style>
