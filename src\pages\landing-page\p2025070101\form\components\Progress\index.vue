<script setup lang="ts">
import { onMounted, ref } from 'vue'

const props = defineProps({
  startVal: {
    type: Number,
    default: 0,
  },
  endVal: {
    type: Number,
    default: 100,
  },
  duration: {
    type: Number,
    default: 1,
  },
})
const stepLineWidth = ref(props.startVal)

function stepInteval() {
  const speed = (props.endVal - props.startVal) / (props.duration * 1000)
  let lastTime = 0

  function animate(timestamp) {
    if (!lastTime)
      lastTime = timestamp
    const deltaTime = timestamp - lastTime
    lastTime = timestamp

    if (stepLineWidth.value < props.endVal) {
      stepLineWidth.value += speed * deltaTime
      stepLineWidth.value = Math.min(stepLineWidth.value, props.endVal)
      requestAnimationFrame(animate)
    }
  }

  requestAnimationFrame(animate)
}

onMounted(() => {
  stepInteval()
})
</script>

<template>
  <div class="top-steps-container">
    <p class="text-18px text-#ffffff mt-36px">
      请填写以下信息，为你申请额度
    </p>
    <div class="mt-13px step-container w-286px p-1.2px bg-#fff rd-7px h-14px">
      <div class="step-line h-12px rd-7px overflow-hidden" :style="{ width: `${stepLineWidth}%` }">
        <p class="text-12px  text-#ffffff text-right  line-height-12px pr-5px">
          {{ Number(stepLineWidth.toFixed(0)) }}%
        </p>
      </div>
    </div>
  </div>
</template>

    <style lang="less" scoped>
    .top-steps-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family:
    PingFangSC,
    PingFang SC;
  .step-line {
    background-image: repeating-linear-gradient(135deg, #489eff, #489eff 1%, #067cff 0, #067cff 2%);
    background-size: 150%;
    animation: ripple 5s linear infinite;
  }

  @keyframes ripple {
    100% {
      background-position: -100% 0;
    }
  }
}
</style>
