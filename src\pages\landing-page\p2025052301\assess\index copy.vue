<script setup lang="ts">
import NavBar from '@/components/NavBar/index.vue'
import AssessStatus from './components/AssessStatus/index.vue'
import AssessSteps from './components/AssessSteps/index.vue'
import PageFooter from '@/components/PageFooter/index.vue'

import { showToast } from 'vant'
import { useTrack } from '../form/hooks/track'
import { carLoansQuery } from '@/api/home'

enum PageStep {
  WAIT = 'wait',
  FAILURE = 'failure',
}

const TRACK_PAGE = 'inProgress'

let timer = null

const router = useRouter()
const route = useRoute()
const orderNo = ref(route.query.orderNo as string || '')
const errMsg = ref(route.query.errMsg as string || '')

const queryResult = ref(null)

const track = useTrack()

const pageStep = computed(() => {
  // 分发等待
  if (queryResult.value?.triageFlag && queryResult.value?.admittanceRustFlag === 0) {
    return PageStep.WAIT
  }
  // 分发失败
  if (queryResult.value?.triageFlag && queryResult.value?.admittanceRustFlag === 2) {
    return PageStep.FAILURE
  }

  if (queryResult.value?.nodeStatus === 1) {
    return PageStep.WAIT
  }

  return PageStep.FAILURE

  // return PageStep.WAIT
})

watch(() => queryResult.value?.step, (newStep) => {
  if (!newStep)
    return

  // 页面展示 - 埋点
  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
      extendJson: {
        currentStatus: pageStep.value === PageStep.FAILURE ? 1 : 2,
        pageType: 4,
      },
    })
  })
})

function trackClick(button) {
  return track.click({
    page: TRACK_PAGE,
    button,
    extendJson: {
      currentStatus: pageStep.value === PageStep.FAILURE ? 1 : 2,
      pageType: 4,
    },
  })
}

// 返回按钮处理
function handleBack() {
  router.back()
}

function getData() {
  return carLoansQuery(orderNo.value)
    .then((res: any) => {
      if (res.code === 200 && res.data) {
        queryResult.value = res.data
      }
    })
    .then(() => {
      handleDistribution()
    })
}

/**
 * 分发逻辑
 */
function handleDistribution() {
  // 分发成功
  if (queryResult.value?.triageFlag && queryResult.value?.admittanceRustFlag === 1) {
    router.replace({
      path: `${route.path.replace('assess', 'distribution')}`,
      query: { ...route.query },
    })
  }
  else if (queryResult.value?.triageFlag && queryResult.value?.admittanceRustFlag === 0) {
    // 分发中
    timer && clearTimeout(timer)
    timer = setTimeout(() => {
      getData()
    }, 5000)
  }
}

onMounted(() => {
  getData()

  timer && clearTimeout(timer)

  timer = setTimeout(() => {
    getData()
  }, 5000)

  if (errMsg.value) {
    showToast(errMsg.value)

    // 删除url中的errMsg参数
    const { errMsg: _, ...query } = route.query
    router.replace({
      path: `${route.path}`,
      query: {
        ...query,
      },
    })
  }
})

onBeforeUnmount(() => {
  timer && clearTimeout(timer)
})
</script>

<template>
  <div class="assess-page">
    <NavBar :background-custom-style="{ backgroundColor: '#fff' }">
      <template #left>
        <div class="flex items-center" @click="handleBack(), trackClick(1)">
          <img class="icon-back" src="@/pages/landing-page/p2025032401/images/icon_back.png">
          <span class="nav-title">申请详情</span>
        </div>
      </template>
    </NavBar>

    <AssessStatus :info="queryResult" :step="pageStep" />

    <AssessSteps
      :info="queryResult" :step="pageStep" @back="handleBack(), trackClick(2)"
    />

    <PageFooter class="footer" />
  </div>
</template>

<style lang='less' scoped>
.assess-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f9ff;
  background: linear-gradient(181deg, #1677ff 0%, #1677ff 100px, #f8faff 400px, #f8faff 100%);

  .icon-back {
    width: 9px;
    height: 15px;
    margin-left: 14px;
    margin-right: 8px;
  }

  .nav-title {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 17px;
    color: #000000;
    line-height: 24px;
  }

  .footer {
    margin-top: auto;
    margin-bottom: 30px;
  }
}
</style>
