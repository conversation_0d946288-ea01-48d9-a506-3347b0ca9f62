<script setup lang="ts">
import { PageType } from '@/enum/page'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import RollingNumber from '@/components/RollingNumber/index.vue'
// 单独引入组件样式
import GaugeEcharts from '@/components/GaugeEcharts/index.vue'
import NoticeBar from '@/components/NoticeBar/index.vue'
import Footer from '@/components/PageFooter/index.vue'
import { useRoute, useRouter } from 'vue-router'

enum TRACK_BUTTONS {
  GET_QUOTA = 2, // 立即领取额度
}
const track = useTrack()
const TRACK_PAGE = 'guideGoForm'
const router = useRouter()
const route = useRoute()

function goForm() {
  normalNavigateTo(PageType.OUTSIDE_CLUE_PAGE)
}
function normalNavigateTo(pageType: number = 6) {
  router.push({
    path: `${route.path.replace('guideGoForm', 'form')}`,
    query: {
      ...route.query,
      pageType,
    },
  })
}
function trackClick(button: TRACK_BUTTONS) {
  try {
    return track.click({
      page: TRACK_PAGE,
      button,
    })
  }
  catch (error) {
    return error
  }
}
onMounted(() => {
  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
    })
  })

  track.stay({
    page: TRACK_PAGE,
  })
})
</script>

<template>
  <div class="page-container pt-146px pb-30px">
    <div class="progress-container">
      <GaugeEcharts :tick-inner-distance="20" :tick-outer-distance="16" :circle-r="122" :endpoint-border-width="1" :duration="1300" />
      <div class="rolling-num">
        <div class="gray-text w-100%">
          贷款额度最高
        </div>
        <div class="rolling-num-blue flex w-100% items-center justify-center">
          <RollingNumber :start="55" :target="100" :duration="1300" :increment="1" :delay="0" />万
        </div>
      </div>
    </div>
    <div class="page-button mt-104px mr-auto ml-auto relative">
      <img
        class="w-325px h-61px block mr-auto ml-auto relative z-1"
        src="@/assets/images/2025070801/btn.png"
        @click="goForm(), trackClick(TRACK_BUTTONS.GET_QUOTA)"
      >
      <img class="absolute top-27px right-6px finger w-50px h-60px" src="@/assets/images/2025070801/finger.png" alt="" srcset="">
    </div>
    <div class="mt-13px w-217px h-23px rd-11px bg-#FFF2CF mr-auto ml-auto">
      <NoticeBar :speed="60" width="199" />
    </div>
    <Footer class="footer mt-213px" />
  </div>
</template>

  <style lang="less" scoped>
  .page-container {
  width: 100%;
  min-height: 812px;
  background-color: #fff;
  background-image: url('@/assets/images/2025070801/login_bg.png');
  background-size: 375px 812px;
  background-repeat: no-repeat;
  font-family:
    PingFangSC,
    PingFang SC;
  .progress-container {
    margin: 0 auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .rolling-num {
      position: absolute;
      width: 100%;
      top: 78px;
      text-align: center;
    }
    .gray-text {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 10px;
      color: #999999;
      line-height: 14px;
    }
    .rolling-num-blue {
      font-family: MiSans, MiSans;
      font-weight: 800;
      font-size: 48px;
      color: #1677ff;
      line-height: 64px;
    }
  }
  .finger {
    z-index: 99;
    animation: tap-animation 0.8s infinite ease-in-out;
  }
  @keyframes tap-animation {
    0%,
    100% {
      transform: translate(-50%, 0) scale(1);
    }
    50% {
      transform: translate(-50%, 10px) scale(0.95);
    }
  }
  .footer {
    &.mt-213px {
      margin-top: 213px;
    }
  }
}
</style>
