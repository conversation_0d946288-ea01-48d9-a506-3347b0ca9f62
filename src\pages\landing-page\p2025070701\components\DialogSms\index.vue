<script setup lang="ts">
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'

const props = defineProps({
  parentForm: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['action'])

enum TRACK_BUTTONS {
  CODE_INPUT_CLICK = 1, // 获取验证码
  CODE_INPUT_FINISH = 2, // 验证码输入完成
  CLOSE = 3, // 关闭
  GET_QUOTA = 4, // 一键获取额度
}

const TRACK_PAGE = 'landingCodePopup'

const track = useTrack()

const verificationCode = ref('')
const codeInputRef = ref<HTMLInputElement>(null)
const codeBtnRef = ref<HTMLButtonElement>(null)
const verificationBtnText = computed(() => props.parentForm.verificationBtnText.value)
const verificationBtnDisabled = computed(() => props.parentForm.verificationBtnDisabled.value)

watch(() => verificationCode.value, (val) => {
  emit('action', { action: 'code', code: val })

  if (val.length === 4) {
    trackClick(TRACK_BUTTONS.CODE_INPUT_FINISH)
  }
}, { immediate: true })

function trackClick(button: TRACK_BUTTONS) {
  try {
    return track.click({
      page: TRACK_PAGE,
      button,
    })
  }
  catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  codeInputRef.value.focus()
  codeBtnRef.value.click()

  track.show({ page: TRACK_PAGE })

  track.stay({ page: TRACK_PAGE })
})
</script>

<template>
  <div class="dialog">
    <img
      class="close" src="@/assets/images/home/<USER>"
      @click="emit('action', { action: 'close' }), trackClick(TRACK_BUTTONS.CLOSE)"
    >
    <div class="title">
      输入验证码
    </div>
    <form class="form" @submit.prevent>
      <div class="form-item">
        <div class="form-input">
          <input ref="codeInputRef" v-model="verificationCode" type="tel" placeholder="请输入验证码" maxlength="4">
        </div>
        <button
          ref="codeBtnRef" class="form-sms" type="button" :disabled="verificationBtnDisabled"
          @click="$emit('action', { action: 'sms' }), trackClick(TRACK_BUTTONS.CODE_INPUT_CLICK)"
        >
          {{ verificationBtnText }}
        </button>
      </div>
      <button class="submit" type="button" @click="$emit('action', { action: 'submit' }), trackClick(TRACK_BUTTONS.GET_QUOTA)">
        获取额度
      </button>
    </form>
  </div>
</template>

<style lang='less' scoped>
.dialog {
  width: 290px;
  height: 214px;
  background: linear-gradient(#c9e7fa 0%, #ffffff 50%, #ffffff 100%);
  border-radius: 16px;
  display: flex;
  flex-direction: column;

  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 21px;
    height: 21px;
    cursor: pointer;
  }

  .title {
    margin: 15px 15px 0;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 600;
    font-size: 22px;
    color: #333333;
    line-height: 30px;
  }

  .form {
    display: flex;
    flex-direction: column;

    .form-item {
      margin: 30px auto 0;
      display: flex;
      align-items: center;
      width: 260px;
      height: 50px;
      background: rgba(22, 119, 255, 0);
      border-radius: 8px;
      border: 1px solid #1677ff;

      .form-input {
        flex: 1;
        display: flex;
        align-items: center;
        width: 260px;
        height: 50px;

        input {
          width: 100px;
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 22px;
          margin-left: 15px;
        }
      }

      .form-sms {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #1677ff;
        line-height: 20px;
        white-space: nowrap;
        margin: 0 15px 0 15px;

        &:disabled {
          color: rgba(22, 119, 255, 0.3);
        }
      }
    }

    .submit {
      width: 260px;
      height: 49px;
      background: #1677ff;
      box-shadow: 0px 2px 5px 0px rgba(47, 121, 255, 0.4);
      border-radius: 9999px;
      display: block;
      margin: 20px auto 0;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 18px;
      color: #ffffff;
      line-height: 25px;
      position: relative;
    }
  }
}
</style>
