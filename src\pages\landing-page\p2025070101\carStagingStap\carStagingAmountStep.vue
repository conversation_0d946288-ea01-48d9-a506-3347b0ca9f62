<script setup lang="ts">
import CarStagingStapLayout from '@/PageComponents/CarStagingStap/components/CarStagingStapLayout.vue'
import CarStagingStapOptions from '@/PageComponents/CarStagingStap/components/CarStagingStapOptions.vue'
import MyNavBar from '@/PageComponents/CarStagingStap/components/MyNavBar.vue'
import { usePageBackInterceptor } from '@/composables/usePageBackInterceptor'
import { TrackClickButtonCarStagingAmountStep } from '@/enum/track'
import { useCarStagingStap } from '@/composables/useCarStagingStap'
import TopContainer from '@/PageComponents/CarStagingStap/components/TopContainer.vue'
import titleImg from '@/assets/images/2025062601/title-1.png'

const pageBackInterceptor = usePageBackInterceptor()

const {
  selectCarStagingAmount,
  goBack,
  trackClick,
  status,
} = useCarStagingStap({
  trackPage: 'carStagingAmountStep',
})

onBeforeUnmount(() => {
  pageBackInterceptor.remove()
})

const carStatusOptions = [
  { label: '0-10万', value: TrackClickButtonCarStagingAmountStep.ZERO_TO_TEN },
  { label: '10-20万', value: TrackClickButtonCarStagingAmountStep.TEN_TO_TWENTY },
  { label: '20-50万', value: TrackClickButtonCarStagingAmountStep.TWENTY_TO_FIFTY },
  { label: '50-100万', value: TrackClickButtonCarStagingAmountStep.FIFTY_TO_HUNDRED },
]

function handleSelect(value: number) {
  selectCarStagingAmount(value)
  trackClick(value)
}
</script>

<template>
  <CarStagingStapLayout record="HZ" footer-tips>
    <template #header>
      <MyNavBar @back="goBack(), trackClick(TrackClickButtonCarStagingAmountStep.BACK)" />
    </template>

    <TopContainer />

    <CarStagingStapOptions
      v-model="status.carStagingAmount"
      :title-img="titleImg"
      :options="carStatusOptions"
      @select="handleSelect"
    />
  </CarStagingStapLayout>
</template>
