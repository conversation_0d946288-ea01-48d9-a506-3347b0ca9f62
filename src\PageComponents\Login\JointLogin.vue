<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { isIOS } from '@/utils/device'
import { TransitionPresets, useTransition } from '@vueuse/core'
import { isInWechat } from '@/utils'

import Footer from '@/components/PageFooter/index.vue'
import IconCheckWhite from '@/components/IconCheckWhite/index.vue'
import pageBackInterceptor from '@/utils/page-back-interceptor'
import { useTrack } from '@/pages/landing-page/p2025032401/form/hooks/track'
import { useSmsLogin } from '@/composables/useSmsLogin'
import { useUnionLogin } from '@/composables/useUnionLogin'
import { useDialog } from '@revfanc/use'
import { useNoBehavior } from '@/composables/useNoBehavior'
import { useProtocol } from '@/composables/useProtocol'
import { loginUpLink, systemConfig } from '@/api/home'
import dayjs from 'dayjs'
import { useUserStore } from '@/stores/modules/user'

import DialogDownload from '@/components/Dialog/DialogDownload.vue'
import DialogAuthorize from '@/PageComponents/Login/components/Dialog/Authorize.vue'
import DialogAuthorizeGuide from '@/PageComponents/Login/components/Dialog/AuthorizeGuide.vue'
import DialogLogin from '@/components/Dialog/DialogLogin.vue'
import DialogSms from '@/components/Dialog/DialogSms.vue'
import { useFormTypeNavigation } from '@/composables/useloginAndAfter'
import { useCarBeforeApplyLoanStep } from '@/composables/useCarBeforeApplyLoanStep'

const props = withDefaults(defineProps<{
  linkType: string
  loginAuthorizeDialogType?: 'default' | 'guide'
}>(), {
  loginAuthorizeDialogType: 'default',
})
// 埋点按钮值定义 1.返回 2.立即领取额度 3.手机号输入点击 4.手机号输入完成 5.验证码获取 6.验证码输入完成 7.勾选协议
enum TRACK_BUTTONS {
  BACK = 1, // 返回按钮
  GET_QUOTA = 2, // 立即领取额度
  PHONE_INPUT_CLICK = 3, // 手机号输入点击
  PHONE_INPUT_FINISH = 4, // 手机号输入完成
  CODE_INPUT_CLICK = 5, // 验证码获取
  CODE_INPUT_FINISH = 6, // 验证码输入完成
  PROTOCOL = 7, // 勾选协议
}
const agreementPageCode = 35
const TRACK_PAGE = 'landingPage'

// 是否点击了返回按钮
const isBack = ref(false)
const source = ref(550000)
const output = useTransition(source, {
  duration: 600,
  transition: TransitionPresets.easeOutExpo,
})
const formattedOutput = computed(() => Math.round(output.value).toLocaleString())
const userStore = useUserStore()
const certificationPop = computed(() => userStore.info.certificationPop)
const track = useTrack()
const dialog = useDialog()

const noBehavior = useNoBehavior()

dialog.interceptors.before.use((opts) => {
  noBehavior.pause()
  return opts
})

dialog.interceptors.after.use((opts) => {
  noBehavior.resume()
  return opts
})

const { showProtocol } = useProtocol()
const {
  phoneNumber,
  phoneNumberValid,
  phoneInputRef,
  verificationCode,
  verificationBtnText,
  verificationBtnDisabled,
  readProtocol,
  getPhoneFromUrl,
  handleSms,
  handleLogin,
  handleNoSmsLogin,
  handleConsentAuthorization,
  agreementDialogConfirmTime,
  handleAgreementClick,
  setInitialPhoneNumber,
  editPhoneNumber,
} = useSmsLogin({ isIndex: true })

const { navigateToForm } = useFormTypeNavigation()

const { status, goStepPage } = useCarBeforeApplyLoanStep({ autoTrack: false })

status.value.carStagingAmount = undefined
status.value.carStatus = undefined
status.value.isReverse = undefined

// 联合登录
const unionLogin = useUnionLogin()

unionLogin.handleLogin().then((res) => {
  console.log('unionLogin-res', res)
  // res.success = true
  const { success, mobile } = res

  if (mobile) {
    phoneNumber.value = mobile
  }

  if (success) {
    handleLoginSuccess()
  }
})

watch(() => phoneNumber.value, (val) => {
  if (val.length === 11) {
    trackClick(TRACK_BUTTONS.PHONE_INPUT_FINISH)
  }
})

onMounted(() => {
  if (!isIOS) {
    pageBackInterceptor.add(handleBack)
  }

  track.refresh().then(() => {
    track.show({
      page: TRACK_PAGE,
    })
  })

  track.stay({
    page: TRACK_PAGE,
  })

  // 用户无操作时,自动弹出登录弹窗
  noBehavior.add({
    callback: showDialogLogin,
  })

  // 如果 URL 中有手机号，设置初始值
  const urlPhone = getPhoneFromUrl()
  if (urlPhone) {
    setInitialPhoneNumber(urlPhone)
  }

  systemConfig('LANDINGPAGE_AGREEMENT_CONFIRM').then((res: any) => {
    if (res.code === 200) {
      readProtocol.value = String(res?.data) === '0'
    }
  })

  // 延迟以确保动画可见
  setTimeout(() => {
    source.value = 1000000
  }, 200)
})

function trackClick(button: TRACK_BUTTONS) {
  try {
    return track.click({
      page: TRACK_PAGE,
      button,
    })
  }
  catch (error) {
    return error
  }
}

// 返回按钮点击事件
async function handleBack() {
  if (isBack.value || isInWechat()) {
    return true
  }
  isBack.value = true
  // 显示下载弹窗
  showDialogDownload()
}

async function navBackIconClick() {
  const shouldBack = await handleBack()

  if (shouldBack) {
    window.history.back()
  }
}
function showUnionLoginAuthorize() {
  return dialog.open({
    render(context) {
      return h(DialogAuthorize, {
        onAction: context.callback,
        phone: phoneNumber.value,
      })
    },
  }).then((res) => {
    if (['confirm', 'cancel', 'close'].includes(res.action)) {
      if (res.action === 'confirm') {
        loginUpLink({ agreementPageCode, confirmDateTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'), additionalLink: 1 })
      }
      setTimeout(() => {
        const isReverse = res.action === 'confirm' ? 0 : (res.action === 'cancel' || res.action === 'close') ? 1 : null
        reverseGoPage(isReverse)
        // goStepPage(props.linkType ? { linkType: props.linkType } : {})
      })
    }
  })
}
function showUnionLoginAuthorizeGuide() {
  return dialog.open({
    render(context) {
      return h(DialogAuthorizeGuide, {
        onAction: context.callback,
      })
    },
  }).then((res) => {
    if (['confirm', 'close'].includes(res.action)) {
      if (res.action === 'confirm') {
        loginUpLink({ agreementPageCode, confirmDateTime: dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'), additionalLink: 1 })
      }
      setTimeout(() => {
        const isReverse = res.action === 'confirm' ? 0 : res.action === 'close' ? 1 : null
        reverseGoPage(isReverse)
      })
    }
  })
}
// 处理登录页后续跳转isReverse:0 正向  1：逆向
function reverseGoPage(isReverse: number) {
  isReverse !== null && (status.value.isReverse = isReverse)
  const pageParams = props.linkType ? { linkType: props.linkType, ...(props.loginAuthorizeDialogType === 'default' ? {} : { noStepPage: false }) } : {}
  goStepPage(pageParams)
}
function showDialogDownload() {
  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogDownload, {
        onAction: context.callback,
      })
    },
  })
}

function showDialogLogin() {
  if (phoneNumber.value)
    return Promise.resolve()

  return dialog.open({
    position: 'bottom',
    render(context) {
      return h(DialogLogin, {
        onAction: context.callback,
      })
    },
  }).then((res) => {
    if (res.action === 'success') {
      showToast('登录成功')

      setTimeout(() => {
        handleLoginSuccess()
      }, 1500)
    }
    return Promise.reject(res)
  })
}

function showDialogSms() {
  return dialog.open({
    render(context) {
      return h(DialogSms, {
        parentForm: {
          verificationBtnText,
          verificationBtnDisabled,
        },
        onAction: context.callback,
      })
    },
    beforeClose: (close, res) => {
      if (res.action === 'sms') {
        handleSms()
      }
      else if (res.action === 'code') {
        verificationCode.value = res.code
      }
      else if (res.action === 'submit') {
        trackClick(TRACK_BUTTONS.GET_QUOTA)
        agreementDialogConfirmTime.value = Date.now()
        handleLogin().then((loginRes) => {
          if (loginRes) {
            close(res)
            handleLoginSuccess()
          }
        })
      }
      else {
        close(res)
      }
    },
  }).finally(() => {
    verificationCode.value = ''
  })
}

async function onSubmit() {
  if (!phoneNumberValid.value) {
    showToast('请输入正确的手机号')
    phoneInputRef.value?.focus()
    return
  }

  // 如果没有勾选协议，显示协议弹窗
  if (!readProtocol.value) {
    const result = await handleConsentAuthorization()
    // 如果用户在协议弹窗点击确认
    if (result?.action === 'confirm') {
      // 判断是否是 URL 中的手机号且未修改
      const urlPhone = getPhoneFromUrl()
      if (urlPhone && !editPhoneNumber.value) {
        handleNoSmsLogin().then(res => res && handleLoginSuccess())
      }
      else {
        // 否则显示验证码弹窗
        showDialogSms()
      }
    }
    return
  }

  // 如果已勾选协议，判断是否是 URL 中的手机号且未修改
  const urlPhone = getPhoneFromUrl()
  if (urlPhone && !editPhoneNumber.value) {
    agreementDialogConfirmTime.value = Date.now()
    handleNoSmsLogin().then(res => res && handleLoginSuccess())
    return
  }

  // 如果手机号已修改或不是从 URL 获取的，走普通验证码登录流程
  if (phoneNumberValid.value && !verificationCode.value) {
    showDialogSms()
    return
  }

  handleLogin().then(res => res && handleLoginSuccess())
}

function handleInputBlur() {
  // 解决 iOS input 键盘收起后页面不归位问题
  if (isIOS) {
    setTimeout(() => {
      window.scrollTo(0, 0)
    }, 200)
  }
}

function handleLoginSuccess() {
  return navigateToForm({ callback: certificationPop.value === 1
    ? props.loginAuthorizeDialogType === 'default' ? showUnionLoginAuthorize : showUnionLoginAuthorizeGuide
    : () => {
        reverseGoPage(1)// 逆向流程
        // goStepPage(props.linkType ? { linkType: props.linkType } : {})
      } })
}
</script>

<template>
  <div class="home-page">
    <NavBar class="nav-bar" :background-custom-style="{ backgroundColor: '#fff' }">
      <template #left>
        <img
          class="back" src="@/assets/images/home/<USER>"
          @click="trackClick(TRACK_BUTTONS.BACK), navBackIconClick()"
        >
      </template>
    </NavBar>

    <div class="header">
      <img class="logo-text" src="@/assets/images/common/logo-text.png">
      <img class="banner" src="@/assets/images/2025062601/index-header-banner.png">
    </div>

    <div class="banner2">
      <span class="amount">
        {{ formattedOutput }}
      </span>
    </div>

    <form class="form" @submit.prevent>
      <div class="form-item">
        <div class="form-input">
          <img v-if="phoneNumberValid" class="icon" src="@/pages/landing-page/p2025040901/images/phone.png">
          <img v-else class="icon" src="@/pages/landing-page/p2025040901/images/phone_2.png">
          <input
            ref="phoneInputRef" v-model="phoneNumber" type="tel" placeholder="请输入领取手机号" maxlength="11"
            @click="trackClick(TRACK_BUTTONS.PHONE_INPUT_CLICK)"
            @blur="handleInputBlur"
          >
        </div>
      </div>
      <button class="submit bounce" type="button" @click="trackClick(TRACK_BUTTONS.GET_QUOTA), onSubmit()" />

      <div class="protocol">
        <!-- <IconCheck
          class="icon" :style="{ color: readProtocol ? '#2F79FF' : '#999999' }"
          @click="(readProtocol = !readProtocol), handleAgreementClick(), trackClick(TRACK_BUTTONS.PROTOCOL)"
        /> -->
        <IconCheckWhite style="margin: 2px 4px 0 0;" :checked="readProtocol" @click="(readProtocol = !readProtocol), handleAgreementClick(), trackClick(TRACK_BUTTONS.PROTOCOL)" />
        <span class="text">
          我已阅读并同意
          《<a @click="showProtocol('privacy-policy')">隐私政策</a>》
          《<a @click="showProtocol('user-agreement')">用户注册服务协议</a>》
          《<a @click="showProtocol('user-share')">个人信息共享授权协议</a>》
        </span>
      </div>
    </form>
    <Footer class="footer" />
  </div>
</template>

<style scoped lang="less">
.home-page {
  width: 375px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ecf5fc;

  .nav-bar {
    .back {
      width: 9px;
      height: 15px;
      margin-left: 14px;
    }
  }

  .header {
    height: 224px;
    position: relative;

    .logo-text {
      width: 93px;
      height: 22px;
      position: absolute;
      top: 26px;
      left: 14px;
      z-index: 1;
    }

    .banner {
      margin-top: -10px;
    }
  }

  .banner2 {
    height: 257px;
    background-size: 100% 100%;
    background-image: url('@/pages/landing-page/p2025040901/images/banner.png');
    margin-top: -70px;
    position: relative;

    .amount {
      position: absolute;
      top: 74px;
      left: 50%;
      transform: translateX(-50%);
      white-space: nowrap;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 48px;
      color: #ffffff;
      line-height: 67px;
    }
  }

  .form {
    width: 345px;
    background: #ffffff;
    box-shadow: 0px 0px 6px 0px rgba(0, 49, 118, 0.19);
    border-radius: 0px 0px 16px 16px;
    display: flex;
    flex-direction: column;
    margin: -1px auto 0;

    .form-item {
      margin: 15px auto 0;
      display: flex;
      align-items: center;
      width: 321px;
      height: 52px;

      .form-input {
        width: auto;
        flex: 1;
        display: flex;
        align-items: center;
        height: 52px;
        background: #f7f8f9;
        border-radius: 9999px;
        border: 1px solid rgba(22, 119, 255, 0.2);

        .icon {
          width: 20px;
          height: 20px;
          margin: 0 10px 0 15px;
          object-fit: contain;
        }

        input {
          width: 100px;
          flex: 1;
          border: none;
          outline: none;
          background: transparent;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 600;
          font-size: 16px;
          color: #333333;
          line-height: 22px;
        }
      }

      .form-sms {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        white-space: nowrap;
        margin: 0 5px 0 15px;

        &:disabled {
          color: #999999;
        }
      }
    }

    .submit {
      width: 331px;
      height: 61px;
      display: block;
      margin: 15px auto 0;
      background-image: url('@/pages/landing-page/p2025040901/images/btn.png');
      background-size: 100% 100%;
    }

    .protocol {
      margin: 8px 19px 15px;
      display: flex;

      .icon {
        width: 12px;
        height: 12px;
        margin-right: 5px;
        display: inline-block;
      }

      .text {
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 10px;
        color: #666666;
        line-height: 14px;

        a {
          text-decoration: underline;
        }
      }
    }
  }

  .footer {
    margin: 30px 0 20px;
  }
}
</style>
