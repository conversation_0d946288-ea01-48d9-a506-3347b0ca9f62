<script setup lang="ts">
import CarStepLayout from '@/PageComponents/CarBeforeApplyLoanStep/components/CarStepLayout.vue'
import CarStepOptions from '@/PageComponents/CarBeforeApplyLoanStep/components/CarStepOptions.vue'
import MyNavBar from '@/PageComponents/CarBeforeApplyLoanStep/components/MyNavBar.vue'
import stepBg from '@/assets/images/p2025070201/step_bg2.png'
import titleImg from '@/assets/images/p2025070201/title-2.png'
import { usePageBackInterceptor } from '@/composables/usePageBackInterceptor'
import { TrackClickButtonCarStatusStep } from '@/enum/track'
import { useCarBeforeApplyLoanStep } from '@/composables/useCarBeforeApplyLoanStep'

const pageBackInterceptor = usePageBackInterceptor()

const isBack = ref(false)

const {
  selectCarStatus,
  goBack,
  trackClick,
  status,
  showDialogLeave,
} = useCarBeforeApplyLoanStep({
  trackPage: 'carBeforeApplyLoanCarStatusStep',
})

const carStagingCarStatusOptions = [
  { label: '全款车', value: TrackClickButtonCarStatusStep.FULL_PAYMENT },
  { label: '按揭已结清', value: TrackClickButtonCarStatusStep.LOAN_CLEARED },
  { label: '按揭未结清', value: TrackClickButtonCarStatusStep.LOAN_UNCLEARED },
]

function handleSelect(value: number) {
  selectCarStatus(value)
  trackClick(value)
}

function handleBack() {
  if (!status.value?.carStatus && !isBack.value) {
    showDialogLeave()
    isBack.value = true
    return false
  }
  return true
}

onMounted(() => {
  pageBackInterceptor.add(handleBack)
})

onBeforeUnmount(() => {
  pageBackInterceptor.remove()
})
</script>

<template>
  <CarStepLayout record="CQ" :step-bg="stepBg">
    <template #header>
      <MyNavBar @back="goBack(), trackClick(TrackClickButtonCarStatusStep.BACK)" />
    </template>

    <CarStepOptions
      v-model="status.carStatus"
      :title-img="titleImg"
      :options="carStagingCarStatusOptions"
      @select="handleSelect"
    />
  </CarStepLayout>
</template>

<style lang="less" scoped>
:deep(.options-container) {
  height: 336px;

  .options {
    gap: 30px;
  }
}
</style>
