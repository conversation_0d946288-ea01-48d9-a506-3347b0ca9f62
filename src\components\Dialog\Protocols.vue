<script setup lang="ts">
import { PROTOCOLS } from '@/constants/index'
import { getInstitutionalProtocols } from '@/api/user'
import { useSessionStorage } from '@vueuse/core'
import VuePdfEmbed from 'vue-pdf-embed'

// 导入 vue-pdf-embed 可选的样式
import 'vue-pdf-embed/dist/styles/annotationLayer.css'
import 'vue-pdf-embed/dist/styles/textLayer.css'

const props = defineProps({
  type: {
    type: String,
    default: 'user-share', // 默认为个人信息共享授权书
    validator: (value: string) => ['user-share', 'privacy-policy', 'user-agreement'].includes(value),
  },
  // 是否显示机构协议
  isInstitutionalAgreementVisible: {
    type: Boolean,
    default: false,
  },
  // 是否隐藏隐私政策、用户注册服务协议
  isHidePrivacyPolicyAndUserAgreement: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['action'])

// 基础协议列表
const baseProtocols = [{
  type: 'privacy-policy',
  title: '隐私政策',
  src: PROTOCOLS.PRIVACY_POLICY_B,
}, {
  type: 'user-agreement',
  title: '用户注册服务协议',
  src: PROTOCOLS.USER_AGREEMENT_B,
}, {
  type: 'user-share',
  title: '个人信息共享授权协议',
  src: PROTOCOLS.USER_SHARE_B,
}]

const institutionalProtocols = useSessionStorage('institutional_protocols', [])

// 获取机构协议列表
async function fetchInstitutionalProtocols() {
  try {
    // 如果session中有数据就不请求
    if (institutionalProtocols.value.length > 0) {
      return
    }

    const res = await getInstitutionalProtocols()
    if (Array.isArray(res.data)) {
      // 转换字段并存入session
      institutionalProtocols.value = res.data.map((item, index) => ({
        // 为每个机构协议创建唯一的type
        type: `institutional-${index}-${item.fileType}`,
        // type: item.fileType,
        title: item.name,
        src: item.url,
      }))
    }
  }
  catch (error) {
    console.error('获取机构协议列表失败:', error)
    institutionalProtocols.value = []
  }
}

// 合并基础协议和机构协议（如果需要显示的话）
const protocolList = computed(() => {
  const list = [...baseProtocols]

  if (props.isInstitutionalAgreementVisible && institutionalProtocols.value.length > 0) {
    list.push(...institutionalProtocols.value)
  }

  // 如果需要隐藏隐私政策和用户注册服务协议，就过滤掉
  if (props.isHidePrivacyPolicyAndUserAgreement) {
    return list.filter(item => !['privacy-policy', 'user-agreement'].includes(item.type))
  }

  return list
})

const activeType = ref(props.type)
const activeSrc = computed(() => protocolList.value.find(item => item.type === activeType.value)?.src)

// 判断当前激活的协议是否为PDF类型
const isPdfProtocol = computed(() => {
  const currentProtocol = protocolList.value.find(item => item.type === activeType.value)
  return currentProtocol?.type === 'pdf'
})

onMounted(() => {
  console.log('protocolList', protocolList.value)
  fetchInstitutionalProtocols()
})
</script>

<template>
  <div class="content">
    <img class="close" src="@/assets/images/home/<USER>" @click="$emit('action', { action: 'close' })">
    <div class="tabs" @touchmove.stop>
      <div
        v-for="item in protocolList" :key="item.type" class="tab" :class="{ active: item.type === activeType }"
        @click="activeType = item.type"
      >
        {{ item.title }}
      </div>
    </div>
    <div class="protocol-content">
      <template v-if="activeSrc">
        <!-- 根据类型决定使用 iframe 还是 VuePdfEmbed -->
        <template v-if="!isPdfProtocol">
          <iframe
            :src="activeSrc"
            width="100%"
            height="100%"
            frameborder="0"
          />
        </template>
        <template v-else>
          <VuePdfEmbed
            :source="activeSrc"
            text-layer
            annotation-layer
          />
        </template>
      </template>
    </div>
  </div>
</template>

<style lang='less' scoped>
.content {
  width: 375px;
  background: #ffffff;
  border-radius: 24px 24px 0px 0px;
  position: relative;

  .close {
    width: 21px;
    height: 21px;
    position: absolute;
    top: -32px;
    right: 12px;
    margin-left: auto;
    z-index: 9;
  }

  .tabs {
    width: 375px;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: scroll;
    padding: 0 15px;

    .tab {
      padding: 15px 0;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 18px;
      color: #666666;
      line-height: 25px;
      white-space: nowrap;

      &:not(:last-child) {
        margin-right: 15px;
      }

      &.active {
        font-weight: 600;
        color: #333333;
      }
    }
  }

  .protocol-content {
    height: 65vh;
    overflow-y: scroll;
    position: relative;
  }
}
</style>
