<script setup lang="ts">
withDefaults(defineProps<{
  title?: string
}>(), {
  title: '',
})
const emit = defineEmits(['back'])
</script>

<template>
  <NavBar class="nav-bar" :background-custom-style="{ backgroundColor: '#fff' }">
    <template #left>
      <img
        class="back" src="@/assets/images/home/<USER>"
        @click="emit('back')"
      >
    </template>
    <template #middle>
      <span class="title">
        {{ title }}
      </span>
    </template>
    <template #right />
  </NavBar>
</template>

<style lang='less' scoped>
.nav-bar {
  color: #fff;

  .back {
    width: 9px;
    height: 15px;
    margin-left: 14px;
  }

  .title {
    color: #fff;
  }
}
</style>
