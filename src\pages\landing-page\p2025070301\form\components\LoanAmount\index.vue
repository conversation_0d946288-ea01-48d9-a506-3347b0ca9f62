<script setup lang="ts">
import { TrackClickButton } from '@/pages/landing-page/p2025070301/form/hooks/track'

const emit = defineEmits(['track'])
const loanAmount = ref(5)

// 5万 10万 20万 50万 100万
enum TRACK_BUTTONS {
  FIVE = 1,
  TEN = 2,
  TWENTY = 3,
  FIFTY = 4,
  HUNDRED = 5,
}

const loanAmountOptions = [
  { label: '5万', value: TRACK_BUTTONS.FIVE, trackValue: TrackClickButton.LOAN_AMOUNT_5 },
  { label: '10万', value: TRACK_BUTTONS.TEN, trackValue: TrackClickButton.LOAN_AMOUNT_10 },
  { label: '20万', value: TRACK_BUTTONS.TWENTY, trackValue: TrackClickButton.LOAN_AMOUNT_20 },
  { label: '50万', value: TRACK_BUTTONS.FIFTY, trackValue: TrackClickButton.LOAN_AMOUNT_50 },
  { label: '100万', value: TRACK_BUTTONS.HUNDRED, trackValue: TrackClickButton.LOAN_AMOUNT_100 },
]

function handleSelect(value: TRACK_BUTTONS) {
  loanAmount.value = value

  emit('track', loanAmountOptions.find(item => item.value === value)?.trackValue)
}
</script>

<template>
  <div class="list">
    <div v-for="item in loanAmountOptions" :key="item.value" class="item" :class="{ active: item.value === loanAmount }" @click="handleSelect(item.value)">
      {{ item.label }}
    </div>
  </div>
</template>

<style scoped lang="less">
.list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.item {
  width: 56px;
  height: 40px;
  background: #f7f7f7;
  border-radius: 8px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;

  &.active {
    background: rgba(22, 119, 255, 0.1);
    border: 1px solid #1677ff;
    color: #1677ff;
  }
}
</style>
