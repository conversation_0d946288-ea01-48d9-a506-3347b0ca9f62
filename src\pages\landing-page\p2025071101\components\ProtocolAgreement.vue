<script setup lang="ts">
import IconCheck from '@/components/Icons/IconCheck.vue'

const emit = defineEmits(['openProtocol', 'openServiceTerms', 'openUserShare', 'protocolInteraction'])
const modelValue = defineModel<boolean>()
function toggleAgreement() {
  modelValue.value = !modelValue.value
  emit('protocolInteraction')
}

function openProtocol() {
  emit('openProtocol')
  emit('protocolInteraction')
}

function openServiceTerms() {
  emit('openServiceTerms')
  emit('protocolInteraction')
}

function openUserShare() {
  emit('openUserShare')
  emit('protocolInteraction')
}
</script>

<template>
  <div class="protocol-agreement" @click="toggleAgreement">
    <div class="agreement-content">
      <IconCheck class="icon" :style="{ color: modelValue ? '#2F79FF' : '#999999' }" />
      <div class="text">
        我已阅读并同意
        <span class="link" @click.stop="openProtocol">《隐私政策》</span>
        <span class="link" @click.stop="openServiceTerms">《用户注册服务协议》</span>
        <span class="link" @click.stop="openUserShare">《个人信息共享授权协议》</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.protocol-agreement {
  margin-top: 15px;
  display: flex;
  justify-content: center;

  .agreement-content {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .icon {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
  }

  .text {
    font-size: 11px;
    color: #666666;
    line-height: 1;
    white-space: nowrap;
  }

  .link {
    color: #666666;
  }
}
</style>
