// To prevent redeclaration error if this file is treated as a script instead of a module
export {}

let timer: ReturnType<typeof setInterval> | null = null

globalThis.onmessage = (event: MessageEvent<{ command: 'start' | 'stop' }>) => {
  const { command } = event.data

  if (command === 'start') {
    if (timer)
      clearInterval(timer)

    timer = setInterval(() => {
      globalThis.postMessage('tick')
    }, 2000)
  }
  else if (command === 'stop') {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }
}
