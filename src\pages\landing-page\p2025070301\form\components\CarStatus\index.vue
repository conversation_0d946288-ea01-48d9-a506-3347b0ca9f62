<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Number,
    default: 1,
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

const statusList = ref([
  {
    id: 1,
    name: '全款',
  },
  {
    id: 2,
    name: '车贷·已结清',
  },
  {
    id: 3,
    name: '车贷·未结清',
  },
])
</script>

<template>
  <div class="list">
    <div
      v-for="(item) in statusList" :key="item.id" class="item" :class="{ active: item.id === props.modelValue }"
      @click="emit('update:modelValue', item.id), emit('change', item.id)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<style lang='less' scoped>
.list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.item {
  width: 103px;
  height: 40px;
  background: #f7f7f7;
  border-radius: 8px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;

  &.active {
    background: rgba(22, 119, 255, 0.1);
    border: 1px solid #1677ff;
    color: #1677ff;
  }
}
</style>
