import { useRoute, useRouter } from 'vue-router'
import { PageType } from '@/enum/page'
import { carLoansInfo, setUserExtend } from '@/api/home'
import type { TrackClickButtonCarStagingAndCarStatus } from '@/enum/track'
import { useDialog } from '@revfanc/use'
import dialogMount from '@/utils/dialog-mount'

const dialog = useDialog()
const DialogLeave = dialogMount(() => import('@/pages/landing-page/components/Dialog/PageInterceptIdentityV2/index.vue'))

interface Options {
  trackPage?: string
  autoTrack?: boolean
}
interface CarStepData {
  carStagingAmount?: number // 车辆分期金额
  carStatus?: number // 车辆状态
  isReverse?: number // 走正向链接 0 走逆向链接
}

const stepConfig = [
  { path: 'carStagingAmountStep' },
  { path: 'carStatusStep' },
  { path: 'form' },
]

const pageBasePath = '/carBeforeApplyLoanStep'

const status = useSessionStorage<CarStepData>('CARSTEP_STATUS', {
  carStagingAmount: 0,
  carStatus: 0,
  isReverse: 1,
})

const info = reactive({
  wishLoansAmount: null,
  vehicleStatus: null,
})

export function useCarBeforeApplyLoanStep(options: Options = {}) {
  const { trackPage = '', autoTrack = true } = options

  const router = useRouter()
  const route = useRoute()

  let track = null

  if (autoTrack) {
    track = useTrack({
      page: trackPage,
      autoShow: true,
      autoStay: true,
    })
  }

  function trackClick(button: TrackClickButtonCarStagingAndCarStatus) {
    track.click({ button })
  }

  const updateUserExtend = (params: any) => {
    console.log('updateUserExtend-params', params)
    return setUserExtend(params).then((res: any) => {
      if (res.code === 200) {
        return res
      }

      if (res?.msg)
        showToast(res.msg)

      return Promise.reject(res)
    })
  }

  const goFormStep = () => {
    const basePath = route.path.substring(0, route.path.lastIndexOf(pageBasePath))
    const nextPath = `${basePath}/${stepConfig[2].path}`
    setTimeout(() => router.push({
      path: nextPath,
      query: { ...route.query, pageType: PageType.OUTSIDE_CLUE_PAGE },
    }), 500)
  }

  const normalNavigateTo = (pageType: number = 6) => {
    router.push({
      path: `${route.path}/form`,
      query: {
        ...route.query,
        pageType,
      },
    })
  }

  const goBack = async () => {
    await router.back()
  }

  const selectCarStagingAmount = (amount: number) => {
    status.value.carStagingAmount = amount

    updateUserExtend({ wishLoansAmount: status.value.carStagingAmount })

    if (info.vehicleStatus) {
      goFormStep()
    }
    else {
      setTimeout(() => router.push({
        path: stepConfig[1].path,
        query: { ...route.query },
      }), 500)
    }
  }

  const selectCarStatus = (newStatus: number) => {
    status.value.carStatus = newStatus

    updateUserExtend({ vehicleStatus: status.value.carStatus })

    goFormStep()
  }

  const showDialogLeave = () => {
    return dialog.open({
      position: 'bottom',
      render(context) {
        return h(DialogLeave, {
          onAction: context.callback,
        })
      },
    })
  }

  const goStepPageHandler = (path: string, params: any = {}) => {
    console.log(`${route.path}${path}`)
    router.push({
      path: `${route.path}${path}`,
      query: {
        ...route.query,
        ...params,
      },
    })
  }

  const goStepPage = async ({ linkType = '', noStepPage = true, pageType = PageType.OUTSIDE_CLUE_PAGE } = {}) => {
    console.log('goStepPage')
    if (status.value.isReverse === 0 || (linkType === 'form' && !noStepPage)) {
      goStepPageHandler('/form', { pageType })
      return
    }

    // 逆向---新流程v2 070901
    if (linkType === 'guideGoForm') {
      goStepPageHandler('/guideGoForm')
      return
    }
    // 逆向---旧流程070201
    const res = await carLoansInfo()
    if (res.code === 200) {
      // if (!status.value.carStatus && res.data.vehicleStatus === 1) {
      //   res.data.vehicleStatus = null
      // }
      // res.data.wishLoansAmount = null
      res.data.vehicleStatus = null

      const { wishLoansAmount, vehicleStatus } = res.data
      info.wishLoansAmount = wishLoansAmount
      info.vehicleStatus = vehicleStatus

      console.log('wishLoansAmount', wishLoansAmount)
      console.log('vehicleStatus', vehicleStatus)

      if (!wishLoansAmount) {
        goStepPageHandler(`${pageBasePath}/carAmountStep`)
      }
      else if (!vehicleStatus) {
        goStepPageHandler(`${pageBasePath}/carStatusStep`)
      }
      else {
        normalNavigateTo(PageType.OUTSIDE_CLUE_PAGE)
      }
    }
  }
  // 非 弹窗跳转 撞库成功 验证码/免验证码/页面等待登录弹窗/协议未勾选 登录 跳转 /form 添加 pageType = 6
  // handleNoSmsLogin().then(res => res && navigateToForm({ callback: () => {
  //   router.push({
  //     path: `${route.path}/form`,
  //     query: {
  //       ...route.query,
  //       pageType: 6,
  //     },
  //   })
  // } }))

  // 弹窗跳转 撞库成功没 "同意并继续" 跳转 调用 goStepPage() , isReverse = 1 存入 sessionStorage
  // 弹窗跳转 撞库成功没 "再考虑考虑" 跳转 调用 goStepPage() , isReverse = 0 存入 sessionStorage

  return {
    status,
    trackClick,
    selectCarStagingAmount,
    selectCarStatus,
    goBack,
    goStepPage,
    normalNavigateTo,
    showDialogLeave,
  }
}
